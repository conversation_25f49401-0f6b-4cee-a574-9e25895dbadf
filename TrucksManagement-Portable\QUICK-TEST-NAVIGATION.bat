@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🚀 اختبار سريع للتنقل
echo ========================================
echo.
echo 🔧 تم إصلاح مشاكل التنقل:
echo    ✅ حذف التعريفات المكررة
echo    ✅ توحيد دالة showPage
echo    ✅ إصلاح مراجع الصفحات
echo    ✅ تحسين معالجة الأخطاء
echo.
echo 🎯 فتح أداة التشخيص...
start "" "NAVIGATION-DIAGNOSTIC.html"

echo.
echo 🚀 فتح التطبيق الرئيسي...
start "" "resources\app\dist\renderer\trucks-app.html"

echo.
echo ========================================
echo    📋 تعليمات الاختبار السريع
echo ========================================
echo.
echo 1️⃣ في التطبيق الرئيسي:
echo    ├── اضغط F12 لفتح Developer Tools
echo    ├── انتقل إلى تبويب Console
echo    ├── جرب الضغط على أزرار التنقل
echo    └── راقب الرسائل في Console
echo.
echo 2️⃣ في أداة التشخيص:
echo    ├── استخدم أزرار الاختبار
echo    ├── راقب سجل التشخيص
echo    ├── تحقق من النتائج
echo    └── اتبع التعليمات المعروضة
echo.
echo 3️⃣ اختبار التنقل:
echo    ├── 🏠 الرئيسية
echo    ├── 🚛 الشاحنات  
echo    ├── 🛣️ الرحلات
echo    ├── 💰 المصروفات
echo    └── 📊 التقارير
echo.
echo ✅ إذا عمل التنقل بشكل صحيح:
echo    ├── ستنتقل بين الصفحات بسلاسة
echo    ├── ستظهر رسائل نجاح في Console
echo    ├── ستتفعل الأزرار بشكل صحيح
echo    └── ستعمل جميع الوظائف
echo.
echo ❌ إذا لم يعمل التنقل:
echo    ├── تحقق من رسائل الخطأ في Console
echo    ├── استخدم أداة التشخيص للمساعدة
echo    ├── تأكد من تحديث الملف
echo    └── جرب إعادة تحميل الصفحة
echo.
pause
