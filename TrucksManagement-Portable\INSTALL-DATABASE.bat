@echo off
chcp 65001 >nul
title تثبيت قاعدة البيانات SQLite

echo.
echo ==========================================
echo    🗄️ تثبيت قاعدة البيانات SQLite
echo ==========================================
echo.

echo 📦 جاري تثبيت المكتبات المطلوبة...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت!
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo.

REM التحقق من وجود npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متاح!
    pause
    exit /b 1
)

echo ✅ npm متاح
echo.

echo 🔧 جاري تثبيت better-sqlite3...
npm install better-sqlite3@9.2.2

if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت better-sqlite3
    echo 🔄 جاري المحاولة مع إعدادات مختلفة...
    npm install better-sqlite3@9.2.2 --build-from-source
    
    if errorlevel 1 (
        echo.
        echo ❌ فشل في التثبيت
        echo 💡 جرب تشغيل الأمر كمدير:
        echo    npm install better-sqlite3 --build-from-source
        pause
        exit /b 1
    )
)

echo.
echo 🔧 جاري تثبيت electron-store...
npm install electron-store@8.1.0

if errorlevel 1 (
    echo ❌ فشل في تثبيت electron-store
    pause
    exit /b 1
)

echo.
echo 📁 جاري إنشاء مجلد البيانات...
if not exist "data" mkdir data

echo.
echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo.
echo 🗄️ قاعدة البيانات SQLite جاهزة للاستخدام
echo 📁 مجلد البيانات: %cd%\data
echo.
echo 🚀 يمكنك الآن تشغيل التطبيق باستخدام:
echo    DIRECT-RUN.bat
echo.

pause
