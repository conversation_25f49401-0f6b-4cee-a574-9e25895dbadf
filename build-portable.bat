@echo off
echo ========================================
echo    بناء النسخة المحمولة - نظام إدارة الشاحنات
echo ========================================
cd /d "C:\TrucksProject"

echo الخطوة 1: تنظيف الملفات القديمة...
if exist "dist" rmdir /s /q dist
if exist "release" rmdir /s /q release

echo الخطوة 2: تثبيت التبعيات...
npm install

echo الخطوة 3: إعادة بناء native modules...
npm run rebuild

echo الخطوة 4: بناء التطبيق...
npm run build

echo الخطوة 5: إنشاء النسخة المحمولة...
npm run pack

echo.
echo ========================================
echo تم إنشاء النسخة المحمولة بنجاح!
echo ستجد الملفات في مجلد: release
echo ========================================

pause
explorer release
