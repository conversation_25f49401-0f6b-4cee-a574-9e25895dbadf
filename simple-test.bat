@echo off
chcp 65001 >nul
echo ========================================
echo    اختبار بسيط لنظام إدارة الشاحنات
echo ========================================
echo.

cd /d "C:\TrucksProject"

echo 1. التحقق من Node.js...
node --version
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    pause
    exit /b 1
)
echo ✓ Node.js متوفر

echo.
echo 2. التحقق من npm...
npm --version
if errorlevel 1 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)
echo ✓ npm متوفر

echo.
echo 3. التحقق من TypeScript...
npx tsc --version
if errorlevel 1 (
    echo تثبيت TypeScript...
    npm install -g typescript
)
echo ✓ TypeScript متوفر

echo.
echo 4. التحقق من ملفات المشروع...
if not exist "package.json" (
    echo ❌ package.json غير موجود
    pause
    exit /b 1
)
echo ✓ package.json موجود

if not exist "electron\main.ts" (
    echo ❌ electron\main.ts غير موجود
    pause
    exit /b 1
)
echo ✓ electron\main.ts موجود

echo.
echo 5. بناء بسيط...
if not exist "dist" mkdir dist

echo بناء main.js...
npx tsc electron\main.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node
echo كود الخروج: %errorlevel%

echo بناء preload.js...
npx tsc electron\preload.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node
echo كود الخروج: %errorlevel%

echo.
echo 6. فحص النتائج...
if exist "dist\main.js" (
    echo ✓ main.js تم إنشاؤه
) else (
    echo ❌ main.js لم يتم إنشاؤه
)

if exist "dist\preload.js" (
    echo ✓ preload.js تم إنشاؤه
) else (
    echo ❌ preload.js لم يتم إنشاؤه
)

echo.
echo محتويات مجلد dist:
dir dist

echo.
echo اضغط أي مفتاح للمتابعة...
pause >nul

if exist "dist\main.js" (
    echo تشغيل التطبيق...
    npx electron dist\main.js
) else (
    echo لا يمكن تشغيل التطبيق - ملفات مفقودة
)

echo.
echo انتهى الاختبار
pause
