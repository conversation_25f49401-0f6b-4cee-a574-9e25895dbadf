@echo off
echo ========================================
echo    إنشاء ملف التثبيت EXE
echo ========================================
cd /d "C:\TrucksProject"

echo الخطوة 1: إيقاف التطبيق الحالي...
taskkill /f /im electron.exe 2>nul

echo الخطوة 2: تثبيت electron-builder...
npm install electron-builder --save-dev

echo الخطوة 3: إنشاء ملف التثبيت...
npx electron-builder --win

echo.
echo ========================================
echo تم إنشاء ملف التثبيت بنجاح!
echo ستجد الملف في مجلد: release
echo ========================================

pause
if exist "release" explorer release
