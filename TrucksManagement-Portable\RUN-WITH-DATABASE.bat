@echo off
chcp 65001 >nul
title نظام إدارة الشاحنات مع قاعدة البيانات SQLite

echo.
echo ==========================================
echo   🚛 نظام إدارة الشاحنات مع SQLite
echo ==========================================
echo.

echo 🔍 جاري فحص النظام...

REM التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تثبيت Node.js أولاً من:
    echo    https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متاح

REM التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود!
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات
if not exist "node_modules" (
    echo 📦 جاري تثبيت المكتبات المطلوبة...
    npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🔧 جرب تشغيل: INSTALL-DATABASE.bat
        pause
        exit /b 1
    )
)

REM التحقق من وجود better-sqlite3
npm list better-sqlite3 >nul 2>&1
if errorlevel 1 (
    echo 🗄️ جاري تثبيت قاعدة البيانات SQLite...
    echo 📦 تثبيت better-sqlite3...
    npm install better-sqlite3@9.2.2
    if errorlevel 1 (
        echo ❌ فشل في تثبيت قاعدة البيانات
        echo 🔧 جرب تشغيل: INSTALL-DATABASE.bat
        pause
        exit /b 1
    )
)

echo ✅ قاعدة البيانات SQLite جاهزة

REM إنشاء مجلد البيانات إذا لم يكن موجوداً
if not exist "data" (
    echo 📁 جاري إنشاء مجلد البيانات...
    mkdir data
)

echo ✅ مجلد البيانات جاهز

echo.
echo 🚀 جاري تشغيل نظام إدارة الشاحنات...
echo 🗄️ مع قاعدة بيانات SQLite المتقدمة
echo.

REM تشغيل التطبيق
npx electron .

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo 🔧 جاري المحاولة بطريقة بديلة...
    echo.
    
    REM محاولة بديلة
    npm start
    
    if errorlevel 1 (
        echo ❌ فشل في التشغيل
        echo.
        echo 💡 تأكد من:
        echo    1. تثبيت Node.js بشكل صحيح
        echo    2. تثبيت المكتبات: npm install
        echo    3. وجود ملفات التطبيق
        echo    4. صلاحيات التشغيل
        echo.
        echo 🔧 جرب تشغيل: INSTALL-DATABASE.bat
        echo.
        pause
    )
)

echo.
echo 👋 تم إغلاق التطبيق
echo 💾 جميع البيانات محفوظة في قاعدة البيانات
echo 📁 موقع البيانات: %cd%\data\trucks_database.db
pause
