const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// إنشاء مجلد البيانات إذا لم يكن موجوداً
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'resources/icon.png'),
        title: 'نظام إدارة الشاحنات - مع قاعدة بيانات SQLite',
        show: false
    });

    mainWindow.loadFile('resources/app/dist/renderer/trucks-app.html');
    
    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // رسالة ترحيب في وحدة التحكم
        console.log('🚛 نظام إدارة الشاحنات جاهز!');
        console.log('📊 قاعدة البيانات SQLite متاحة');
        console.log(`📁 مجلد البيانات: ${dataDir}`);
    });
    
    // فتح أدوات المطور للتشخيص
    mainWindow.webContents.openDevTools();

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        console.log('🔒 تم إغلاق التطبيق');
    });

    return mainWindow;
}

// التعامل مع IPC للتفاعل مع قاعدة البيانات
ipcMain.handle('get-app-path', () => {
    return app.getAppPath();
});

ipcMain.handle('get-user-data-path', () => {
    return app.getPath('userData');
});

ipcMain.handle('get-data-dir', () => {
    return dataDir;
});

// إعداد التطبيق
app.whenReady().then(() => {
    console.log('⚡ Electron جاهز');
    createWindow();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        console.log('👋 إنهاء التطبيق');
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// التعامل مع الأخطاء
process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ رفض غير معالج:', reason);
});
