@echo off
chcp 65001 >nul
echo ========================================
echo    نظام إدارة الشاحنات - الإصدار النهائي
echo ========================================
echo.

cd /d "C:\TrucksProject"

echo الخطوة 1: بناء الملفات...
npm run build:main
if errorlevel 1 (
    echo خطأ في البناء!
    pause
    exit /b 1
)

echo.
echo الخطوة 2: تشغيل التطبيق...
echo سيفتح التطبيق الآن مع بيانات تجريبية...
npm start

pause
