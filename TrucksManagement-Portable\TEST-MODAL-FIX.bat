@echo off
echo.
echo ========================================
echo 🧪 اختبار إصلاح إغلاق النوافذ
echo ========================================
echo.
echo 📋 خطوات الاختبار:
echo.
echo 1️⃣  سيتم تشغيل التطبيق
echo 2️⃣  اختبر إضافة شاحنة جديدة
echo 3️⃣  تحقق من إغلاق النافذة تلقائياً
echo 4️⃣  اختبر إضافة مصروف جديد
echo 5️⃣  تحقق من إغلاق النافذة تلقائياً
echo.
echo ✅ النتيجة المتوقعة:
echo    - النافذة تُغلق خلال نصف ثانية
echo    - رسالة نجاح تظهر
echo    - البيانات تُحفظ في القائمة
echo.
echo 🚀 بدء الاختبار...
echo.
pause

echo 📱 تشغيل التطبيق...
start "" npx electron resources/app

echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 📋 تعليمات الاختبار:
echo.
echo 🚛 لاختبار الشاحنات:
echo    1. انقر "إضافة شاحنة"
echo    2. املأ: رقم اللوحة + اسم السائق
echo    3. انقر "إضافة الشاحنة"
echo    4. راقب إغلاق النافذة تلقائياً
echo.
echo 💰 لاختبار المصروفات:
echo    1. انقر "إضافة مصروف"
echo    2. املأ: الشاحنة + النوع + المبلغ + الوصف
echo    3. انقر "إضافة المصروف"
echo    4. راقب إغلاق النافذة تلقائياً
echo.
echo ⏱️  توقيت الإغلاق: نصف ثانية
echo.
echo 🎯 إذا لم تُغلق النافذة تلقائياً، فهناك مشكلة!
echo.
pause
