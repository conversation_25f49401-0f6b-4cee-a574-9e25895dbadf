@echo off
echo.
echo ========================================
echo 📊 اختبار إصلاح التقارير المالية
echo ========================================
echo.
echo 🐛 المشكلة المُبلغ عنها:
echo    - التقارير المالية تظهر أرقام ثابتة
echo    - لا تتحدث عند إدخال بيانات جديدة
echo    - البيانات لا تعكس الواقع الفعلي
echo.
echo ✅ الإصلاحات المطبقة:
echo    ✅ إزالة الشرط الذي يمنع التحديث
echo    ✅ تحديث البيانات في صفحة الترحيب
echo    ✅ تحديث التقارير بعد كل عملية إضافة
echo    ✅ حساب البيانات من المصادر الفعلية
echo.
echo 📋 خطة الاختبار:
echo.
echo 1️⃣  سيتم تشغيل التطبيق
echo 2️⃣  فحص التقارير قبل إدخال البيانات
echo 3️⃣  إضافة شاحنة ورحلة ومصروف
echo 4️⃣  فحص تحديث التقارير
echo.
pause

echo 📱 تشغيل التطبيق...
start "" npx electron resources/app

echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 🔍 تعليمات الاختبار المفصلة:
echo.
echo ==========================================
echo 📊 المرحلة الأولى: فحص التقارير الأولية
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر على تبويب "التقارير"
echo    2. لاحظ القيم في:
echo       - إجمالي الإيرادات
echo       - إجمالي المصروفات
echo       - صافي الربح
echo       - هامش الربح
echo    3. لاحظ القيم في صفحة الترحيب أيضاً
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ إذا لم تكن هناك بيانات، يجب أن تظهر أصفار
echo    ✅ إذا كانت هناك بيانات، يجب أن تظهر القيم الفعلية
echo    ✅ لا توجد أرقام ثابتة مثل 8,539 أو 1,300
echo.
echo ==========================================
echo 🚛 المرحلة الثانية: إضافة شاحنة
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر على تبويب "الشاحنات"
echo    2. انقر "إضافة شاحنة جديدة"
echo    3. املأ البيانات:
echo       - رقم اللوحة: تست 4040
echo       - اسم السائق: سائق التقارير
echo       - الحالة: نشطة
echo    4. انقر "إضافة"
echo    5. انتقل فوراً لصفحة التقارير
echo    6. لاحظ أن البيانات لا تزال كما هي (لأنه لا توجد رحلات بعد)
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ الشاحنة تُضاف بنجاح
echo    ✅ التقارير لا تتغير (لأنه لا توجد رحلات أو مصروفات)
echo.
echo ==========================================
echo 🚚 المرحلة الثالثة: إضافة رحلة
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر على تبويب "الرحلات"
echo    2. انقر "تسجيل رحلة جديدة"
echo    3. املأ البيانات:
echo       - الشاحنة: تست 4040 - سائق التقارير
echo       - نوع المادة: رمل
echo       - الكمية: 10
echo       - السعر: 100
echo       - موقع التحميل: محجر التقارير
echo       - موقع التفريغ: مشروع التقارير
echo    4. انقر "تسجيل الرحلة"
echo    5. انتقل فوراً لصفحة التقارير
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ إجمالي الإيرادات يصبح: 1,150 ريال (10 × 100 × 1.15)
echo    ✅ إجمالي المصروفات يبقى: 0 ريال
echo    ✅ صافي الربح يصبح: 1,150 ريال
echo    ✅ هامش الربح يصبح: 100%%
echo.
echo ==========================================
echo 💰 المرحلة الرابعة: إضافة مصروف
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر على تبويب "المصروفات"
echo    2. انقر "إضافة مصروف جديد"
echo    3. املأ البيانات:
echo       - الشاحنة: تست 4040 - سائق التقارير
echo       - نوع المصروف: وقود
echo       - المبلغ: 150
echo       - الوصف: وقود للاختبار
echo    4. انقر "إضافة المصروف"
echo    5. انتقل فوراً لصفحة التقارير
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ إجمالي الإيرادات يبقى: 1,150 ريال
echo    ✅ إجمالي المصروفات يصبح: 150 ريال
echo    ✅ صافي الربح يصبح: 1,000 ريال (1,150 - 150)
echo    ✅ هامش الربح يصبح: 87.0%% تقريباً
echo.
echo ==========================================
echo 🔄 المرحلة الخامسة: اختبار التحديث المستمر
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. أضف رحلة أخرى:
echo       - نفس الشاحنة
echo       - الكمية: 5
echo       - السعر: 80
echo    2. فوراً بعد الإضافة، تحقق من التقارير
echo    3. أضف مصروف آخر:
echo       - نفس الشاحنة
echo       - المبلغ: 50
echo    4. فوراً بعد الإضافة، تحقق من التقارير
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ بعد الرحلة الثانية:
echo       - إجمالي الإيرادات: 1,610 ريال (1,150 + 460)
echo    ✅ بعد المصروف الثاني:
echo       - إجمالي المصروفات: 200 ريال (150 + 50)
echo       - صافي الربح: 1,410 ريال (1,610 - 200)
echo.
echo ==========================================
echo 🏠 المرحلة السادسة: اختبار صفحة الترحيب
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر على تبويب "الرئيسية"
echo    2. تحقق من الإحصائيات السريعة في الأسفل
echo    3. تأكد من تطابقها مع التقارير المالية
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ نفس الأرقام في صفحة التقارير
echo    ✅ تحديث فوري ومتزامن
echo    ✅ لا توجد أرقام ثابتة أو قديمة
echo.
echo ==========================================
echo 📊 تقييم النتائج
echo ==========================================
echo.
echo ✅ الاختبار ناجح إذا:
echo    - التقارير تبدأ بأصفار أو قيم فعلية
echo    - التقارير تتحدث فوراً بعد إضافة البيانات
echo    - الحسابات صحيحة ودقيقة
echo    - صفحة الترحيب متزامنة مع التقارير
echo    - لا توجد أرقام ثابتة أو وهمية
echo.
echo ❌ الاختبار فاشل إذا:
echo    - التقارير تظهر أرقام ثابتة مثل 8,539
echo    - التقارير لا تتحدث بعد إضافة البيانات
echo    - الحسابات خاطئة أو غير دقيقة
echo    - عدم تزامن بين الصفحات المختلفة
echo    - ظهور أخطاء في الكونسول
echo.
echo 🎉 إذا نجحت جميع الاختبارات:
echo    - التقارير المالية تعمل بشكل صحيح
echo    - البيانات حية ومتحدثة
echo    - النظام جاهز للاستخدام الفعلي
echo.
echo 💡 ملاحظات مهمة:
echo    - الحسابات تشمل ضريبة 15%% على الرحلات
echo    - الرحلة = الكمية × السعر × 1.15
echo    - صافي الربح = إجمالي الإيرادات - إجمالي المصروفات
echo    - هامش الربح = (صافي الربح ÷ إجمالي الإيرادات) × 100
echo.
pause
