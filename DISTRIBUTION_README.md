# 📦 دليل إنشاء ملف التثبيت - نظام إدارة الشاحنات

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية إنشاء ملف تثبيت .exe كامل لنظام إدارة الشاحنات مع قاعدة بيانات SQLite3.

## 🔧 المتطلبات المسبقة

### 1. البرامج المطلوبة:
- **Node.js 18+** - من https://nodejs.org/
- **Python 3.8+** - من https://python.org/ (مطلوب لبناء sqlite3)
- **Visual Studio Build Tools** - من https://visualstudio.microsoft.com/downloads/

### 2. إعداد البيئة:
```cmd
# تثبيت windows-build-tools (اختياري)
npm install -g windows-build-tools

# أو تثبيت Visual Studio Build Tools يدوياً
```

## 🚀 خطوات إنشاء ملف التثبيت

### الطريقة الأولى (الأسهل):
انقر نقراً مزدوجاً على ملف `build-installer.bat`

### الطريقة الثانية (يدوي):
```cmd
cd C:\TrucksProject

# 1. تنظيف الملفات القديمة
rmdir /s /q dist
rmdir /s /q release

# 2. تثبيت التبعيات
npm install

# 3. إعادة بناء native modules
npm run rebuild

# 4. بناء التطبيق
npm run build

# 5. إنشاء ملف التثبيت
npm run dist:win
```

## 📁 ملفات التوزيع المُنشأة

بعد اكتمال البناء، ستجد في مجلد `release`:

### ملف التثبيت:
- `نظام إدارة الشاحنات Setup 1.0.0.exe` - ملف التثبيت الكامل

### النسخة المحمولة:
- `win-unpacked/` - مجلد النسخة المحمولة
- `نظام إدارة الشاحنات 1.0.0.exe` - الملف التنفيذي

## 🎯 مميزات ملف التثبيت

### ✅ ما يتضمنه:
- **التطبيق الكامل** مع جميع التبعيات
- **قاعدة بيانات SQLite3** جاهزة للاستخدام
- **بيانات تجريبية** (3 شاحنات، رحلات، مصروفات)
- **اختصارات سطح المكتب** وقائمة ابدأ
- **إزالة نظيفة** عند إلغاء التثبيت

### 🔧 إعدادات التثبيت:
- **مجلد التثبيت**: `C:\Program Files\TrucksManagement\`
- **قاعدة البيانات**: `%USERPROFILE%\AppData\Roaming\trucks-management-system\`
- **الاختصارات**: سطح المكتب + قائمة ابدأ
- **الحجم**: ~150-200 MB

## 📋 اختبار ملف التثبيت

### قبل التوزيع:
1. **اختبر التثبيت** على جهاز نظيف
2. **تحقق من عمل قاعدة البيانات**
3. **اختبر جميع الوظائف**
4. **تحقق من إلغاء التثبيت**

### الاختبار على أنظمة مختلفة:
- ✅ Windows 10 (64-bit)
- ✅ Windows 11 (64-bit)
- ⚠️ Windows 7/8 (قد يحتاج تحديثات إضافية)

## 🔍 حل المشاكل الشائعة

### مشكلة: "node-gyp rebuild failed"
**الحل:**
```cmd
npm install -g node-gyp
npm config set python python3
npm run rebuild
```

### مشكلة: "sqlite3 bindings not found"
**الحل:**
```cmd
npm rebuild sqlite3 --build-from-source
npm run rebuild
```

### مشكلة: "electron-builder failed"
**الحل:**
```cmd
npm cache clean --force
npm install
npm run build
```

## 📦 التوزيع والنشر

### للمستخدمين النهائيين:
1. **شارك ملف** `نظام إدارة الشاحنات Setup 1.0.0.exe`
2. **المستخدم ينقر نقراً مزدوجاً** على الملف
3. **يتبع معالج التثبيت**
4. **يشغل التطبيق** من سطح المكتب أو قائمة ابدأ

### متطلبات النظام للمستخدم النهائي:
- **Windows 10/11** (64-bit)
- **4 GB RAM** (الحد الأدنى)
- **500 MB** مساحة فارغة
- **لا يحتاج Node.js أو Python** - كل شيء مدمج!

## 🎉 النتيجة النهائية

**ملف تثبيت واحد يحتوي على:**
- ✅ نظام إدارة الشاحنات الكامل
- ✅ قاعدة بيانات SQLite3 مدمجة
- ✅ واجهة عربية جميلة
- ✅ جميع الوظائف (شاحنات، رحلات، مصروفات، تقارير)
- ✅ بيانات تجريبية جاهزة
- ✅ تثبيت وإزالة سهل

---

**الآن يمكن توزيع النظام بسهولة لأي مستخدم!** 🚛✨
