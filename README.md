# نظام إدارة الشاحنات - Trucks Management System

نظام شامل لإدارة شاحنات النقل الثقيل مبني بتقنية Electron و React مع قاعدة بيانات SQLite.

## 🚛 المميزات الرئيسية

### 📋 إدارة الشاحنات
- تسجيل بيانات الشاحنات (رقم اللوحة، السائق، الحالة)
- تتبع حالة الشاحنات (نشطة، صيانة، غير متاحة)
- إضافة ملاحظات لكل شاحنة

### 🛣️ تسجيل الرحلات
- تسجيل تفاصيل كل رحلة (التاريخ، نوع المادة، الكمية)
- حساب تلقائي للأسعار والضرائب
- تتبع مواقع التحميل والتنزيل
- دعم أنواع مختلفة من المواد (3/4، Base Course، Sub Base، إلخ)

### 💰 إدارة المصروفات
- تسجيل مصروفات متنوعة (وقود، صيانة، رواتب)
- ربط المصروفات بالشاحنات والسائقين
- إمكانية إرفاق مستندات ومرفقات
- تصنيف المصروفات حسب النوع

### 📊 التقارير والتحليلات
- **التقرير الشهري**: تحليل شامل لكل شاحنة
- **تقرير الربحية**: تتبع الأرباح والخسائر
- **مقارنة الشاحنات**: ترتيب الشاحنات حسب الأداء
- **تحليل المواد**: إحصائيات أنواع المواد المنقولة
- **تحليل المواقع**: أكثر المواقع نشاطاً
- رسوم بيانية تفاعلية ومرئية

## 🛠️ التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript
- **Desktop**: Electron 28
- **Database**: SQLite + better-sqlite3
- **UI Framework**: Tailwind CSS
- **Forms**: React Hook Form
- **Charts**: Recharts
- **Icons**: Lucide React
- **Build Tool**: Vite

## 📦 التثبيت والتشغيل

### متطلبات النظام
- Node.js 18+ 
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd TrucksProject
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل المشروع في وضع التطوير**
```bash
npm run dev
```

4. **بناء المشروع للإنتاج**
```bash
npm run build
```

5. **إنشاء ملف تثبيت**
```bash
npm run dist
```

## 🗃️ هيكل قاعدة البيانات

### جدول الشاحنات (trucks)
- `id`: المعرف الفريد
- `plate_number`: رقم اللوحة
- `driver_name`: اسم السائق
- `status`: الحالة (active/maintenance/inactive)
- `notes`: ملاحظات

### جدول الرحلات (trips)
- `id`: المعرف الفريد
- `truck_id`: معرف الشاحنة
- `trip_date`: تاريخ الرحلة
- `material_type`: نوع المادة
- `quantity_ton`: الكمية بالطن
- `price_per_ton`: سعر الطن
- `total`: الإجمالي شامل الضريبة
- `loading_location`: مكان التحميل
- `unloading_location`: مكان التنزيل

### جدول المصروفات (expenses)
- `id`: المعرف الفريد
- `truck_id`: معرف الشاحنة
- `expense_date`: تاريخ المصروف
- `expense_type`: نوع المصروف
- `amount`: المبلغ
- `description`: الوصف
- `driver_name`: اسم السائق

## 🎯 الاستخدام

### إضافة شاحنة جديدة
1. انتقل إلى صفحة "الشاحنات"
2. اضغط على "إضافة شاحنة جديدة"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### تسجيل رحلة
1. انتقل إلى صفحة "الرحلات"
2. اضغط على "إضافة رحلة جديدة"
3. اختر الشاحنة وأدخل تفاصيل الرحلة
4. سيتم حساب الإجمالي تلقائياً

### عرض التقارير
1. انتقل إلى صفحة "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية والمرشحات
4. اعرض النتائج والرسوم البيانية

## 🔧 التخصيص

يمكن تخصيص النظام بسهولة من خلال:
- إضافة أنواع مواد جديدة في `TripForm.tsx`
- تعديل أنواع المصروفات في `ExpenseForm.tsx`
- إضافة مواقع جديدة في قوائم المواقع
- تخصيص التقارير في `ReportsPage.tsx`

## 📱 المميزات المستقبلية

- [ ] نظام المستخدمين والصلاحيات
- [ ] تصدير التقارير إلى PDF/Excel
- [ ] تنبيهات الصيانة الدورية
- [ ] حساب استهلاك الوقود
- [ ] تطبيق موبايل مصاحب
- [ ] نسخ احتياطي تلقائي
- [ ] تتبع GPS للشاحنات

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**تم تطوير هذا النظام خصيصاً لإدارة شاحنات النقل الثقيل بكفاءة وسهولة** 🚛✨
