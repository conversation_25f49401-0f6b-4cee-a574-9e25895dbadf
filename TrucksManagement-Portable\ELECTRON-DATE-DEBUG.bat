@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔍 تشخيص مشكلة التاريخ - Electron App
echo ========================================
echo.
echo 🎯 التطبيق: Electron Desktop Application
echo 📁 الملف الرئيسي: resources/app/dist/renderer/trucks-app.html
echo 🔧 المشكلة: التاريخ المختار لا يُحفظ، يظهر تاريخ اليوم
echo.
echo ========================================
echo    🚀 خطوات التشخيص السريع
echo ========================================
echo.
echo 1️⃣ شغل التطبيق:
echo    └── استخدم DIRECT-RUN.bat أو npm start
echo.
echo 2️⃣ افتح Developer Tools:
echo    ├── اضغط F12 أو Ctrl+Shift+I
echo    ├── أو اضغط Ctrl+Shift+J
echo    ├── اذهب لتبويب Console
echo    └── امسح الرسائل (Clear Console)
echo.
echo 3️⃣ امسح localStorage:
echo    ├── في Developer Tools اذهب لتبويب Application
echo    ├── في القائمة اليسرى اختر Storage → Local Storage
echo    ├── اختر file:// (أو localhost)
echo    ├── امسح جميع المفاتيح
echo    └── أعد تحميل الصفحة (F5)
echo.
echo 4️⃣ اختبر تسجيل رحلة:
echo    ├── اذهب لصفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── غير التاريخ من تاريخ اليوم إلى 2025-06-01
echo    ├── املأ باقي البيانات:
echo    │   ├── الشاحنة: أي شاحنة متاحة
echo    │   ├── المادة: رمل
echo    │   ├── الكمية: 10
echo    │   ├── السعر: 100
echo    │   ├── موقع التحميل: اختبار
echo    │   └── موقع التفريغ: اختبار
echo    ├── اضغط "تسجيل الرحلة"
echo    └── راقب رسائل Console
echo.
echo 5️⃣ ابحث عن هذه الرسائل:
echo    ├── "📅 التاريخ المختار: 2025-06-01"
echo    ├── "🆕 الرحلة الجديدة: {date: '2025-06-01'}"
echo    ├── "📊 المصفوفة بعد الإضافة: X رحلة"
echo    ├── "💾 تم حفظ البيانات في localStorage"
echo    ├── "📂 localStorage trips: موجود"
echo    ├── "📅 تواريخ الرحلات المحفوظة: [..., X: 2025-06-01]"
echo    └── "📅 عرض رحلة: X تاريخ: 2025-06-01"
echo.
echo 6️⃣ تحقق من النتيجة:
echo    ├── انظر للرحلة الجديدة في قائمة الرحلات
echo    ├── هل يظهر التاريخ 2025-06-01 أم تاريخ اليوم؟
echo    ├── تحقق من localStorage في Application tab
echo    └── ابحث عن مفتاح "trips" وتحقق من التواريخ
echo.
echo ========================================
echo    🔍 تحليل المشكلة
echo ========================================
echo.
echo إذا ظهر في Console:
echo.
echo ✅ "📅 التاريخ المختار: 2025-06-01"
echo    └── الحقل يقرأ التاريخ بشكل صحيح
echo.
echo ✅ "🆕 الرحلة الجديدة: {date: '2025-06-01'}"
echo    └── الكائن يُنشأ بالتاريخ الصحيح
echo.
echo ✅ "📅 تواريخ الرحلات المحفوظة: [..., X: 2025-06-01]"
echo    └── التاريخ محفوظ في localStorage بشكل صحيح
echo.
echo ❌ إذا ظهر تاريخ اليوم في أي مرحلة:
echo    └── هناك مشكلة في تلك المرحلة تحديداً
echo.
echo ========================================
echo    🛠️ حلول سريعة
echo ========================================
echo.
echo 🔧 إذا كان التاريخ صحيح في Console لكن خطأ في العرض:
echo    ├── المشكلة في displayTrips أو loadDataFromLocalStorage
echo    ├── البيانات الافتراضية قد تُعيد الكتابة
echo    └── جرب مسح localStorage تماماً وأعد المحاولة
echo.
echo 🔧 إذا كان التاريخ خطأ من البداية:
echo    ├── المشكلة في قراءة حقل trip-date
echo    ├── تحقق من وجود الحقل في DOM
echo    ├── جرب: document.getElementById('trip-date').value
echo    └── في Console للتأكد من القيمة
echo.
echo 🔧 إذا كانت المشكلة في localStorage:
echo    ├── Electron قد يحفظ في مكان مختلف
echo    ├── تحقق من userData directory
echo    └── قد تحتاج لاستخدام electron-store بدلاً من localStorage
echo.
echo ========================================
echo    💡 نصائح خاصة بـ Electron
echo ========================================
echo.
echo 📁 مسار البيانات في Electron:
echo    ├── Windows: %APPDATA%\trucks-management-system
echo    ├── macOS: ~/Library/Application Support/trucks-management-system
echo    └── Linux: ~/.config/trucks-management-system
echo.
echo 🔧 أدوات تشخيص Electron:
echo    ├── اضغط Ctrl+Shift+I لفتح DevTools
echo    ├── استخدم Console للتشخيص
echo    ├── تحقق من Application → Local Storage
echo    └── راجع Network tab إذا كان هناك طلبات
echo.
echo 🎯 إذا استمرت المشكلة:
echo    ├── تأكد من أن nodeIntegration: true
echo    ├── تحقق من contextIsolation: false
echo    ├── راجع إعدادات webPreferences في main.js
echo    └── جرب إعادة بناء التطبيق (npm run build)
echo.
pause
