@echo off
echo ========================================
echo    إنشاء النسخة المحمولة
echo ========================================
cd /d "C:\TrucksProject"

echo الخطوة 1: إنشاء مجلد النسخة المحمولة...
if exist "TrucksManagement-Portable" rmdir /s /q "TrucksManagement-Portable"
mkdir "TrucksManagement-Portable"

echo الخطوة 2: نسخ ملفات التطبيق...
xcopy /E /I /Y "dist" "TrucksManagement-Portable\resources\app\dist"
copy "package-simple.json" "TrucksManagement-Portable\resources\app\package.json"

echo الخطوة 3: نسخ ملفات Electron...
if exist "node_modules\electron\dist" (
    xcopy /E /I /Y "node_modules\electron\dist\*" "TrucksManagement-Portable\"
) else (
    echo تحميل Electron...
    npx electron --version
    xcopy /E /I /Y "%USERPROFILE%\.cache\electron\*" "TrucksManagement-Portable\"
)

echo الخطوة 4: إنشاء ملف التشغيل...
echo @echo off > "TrucksManagement-Portable\نظام إدارة الشاحنات.bat"
echo cd /d "%%~dp0" >> "TrucksManagement-Portable\نظام إدارة الشاحنات.bat"
echo electron.exe resources\app >> "TrucksManagement-Portable\نظام إدارة الشاحنات.bat"

echo.
echo ========================================
echo تم إنشاء النسخة المحمولة بنجاح!
echo المجلد: TrucksManagement-Portable
echo ========================================

pause
explorer "TrucksManagement-Portable"
