@echo off
chcp 65001 >nul
title اختبار نظام إدارة الشاحنات قبل البناء

echo.
echo ==========================================
echo   🧪 اختبار النظام قبل البناء النهائي
echo ==========================================
echo.

echo 🔍 جاري فحص النظام...

REM التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تثبيت Node.js أولاً من:
    echo    https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متاح

REM التحقق من وجود الملفات الأساسية
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود!
    pause
    exit /b 1
)

if not exist "main.js" (
    echo ❌ ملف main.js غير موجود!
    pause
    exit /b 1
)

if not exist "resources\app\dist\renderer\trucks-app.html" (
    echo ❌ ملف التطبيق الرئيسي غير موجود!
    pause
    exit /b 1
)

echo ✅ الملفات الأساسية موجودة

REM التحقق من وجود المكتبات
if not exist "node_modules" (
    echo 📦 جاري تثبيت المكتبات...
    npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ المكتبات متاحة

REM التحقق من وجود better-sqlite3
npm list better-sqlite3 >nul 2>&1
if errorlevel 1 (
    echo 🗄️ جاري تثبيت قاعدة البيانات...
    npm install better-sqlite3@9.2.2
    if errorlevel 1 (
        echo ❌ فشل في تثبيت قاعدة البيانات
        echo 💡 جرب تشغيل: INSTALL-DATABASE.bat
        pause
        exit /b 1
    )
)

echo ✅ قاعدة البيانات جاهزة

REM إنشاء مجلد البيانات
if not exist "data" (
    echo 📁 جاري إنشاء مجلد البيانات...
    mkdir data
)

echo ✅ مجلد البيانات جاهز

echo.
echo 🚀 جاري تشغيل النظام للاختبار...
echo.
echo 📋 تحقق من النقاط التالية:
echo    1. ✅ التطبيق يفتح بدون أخطاء
echo    2. ✅ الواجهة العربية تظهر بشكل صحيح
echo    3. ✅ يمكن إضافة شاحنة جديدة
echo    4. ✅ يمكن تسجيل رحلة جديدة
echo    5. ✅ يمكن إضافة مصروف جديد
echo    6. ✅ التقارير تعمل بشكل صحيح
echo    7. ✅ قاعدة البيانات تحفظ البيانات
echo    8. ✅ النسخ الاحتياطية تعمل
echo.

echo ⏳ سيتم تشغيل التطبيق خلال 3 ثوان...
timeout /t 3 /nobreak >nul

REM تشغيل التطبيق
npx electron .

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo.
    echo 💡 تأكد من:
    echo    1. تثبيت جميع المكتبات بشكل صحيح
    echo    2. وجود ملفات التطبيق
    echo    3. صلاحيات التشغيل
    echo.
    pause
    exit /b 1
)

echo.
echo 🎉 تم إغلاق التطبيق
echo.

echo 📋 هل تم اختبار جميع المميزات بنجاح؟
echo.
set /p test_result="اكتب 'نعم' إذا كان كل شيء يعمل بشكل صحيح: "

if /i "%test_result%"=="نعم" (
    echo.
    echo ✅ ممتاز! النظام جاهز للبناء النهائي
    echo.
    echo 🚀 يمكنك الآن تشغيل:
    echo    BUILD-COMMERCIAL.bat
    echo.
    echo 📦 لإنشاء ملف EXE قابل للتوزيع التجاري
    echo.
) else (
    echo.
    echo ⚠️ يرجى إصلاح المشاكل قبل البناء النهائي
    echo.
    echo 🔧 راجع الأخطاء وأعد الاختبار
    echo.
)

pause
