========================================
    حل مشكلة عدم فتح التطبيق
========================================

🔧 المشكلة: CMD يغلق ولا يفتح التطبيق

🚀 الحلول (جرب بالترتيب):

1. ✅ الحل الأول (الأفضل):
   انقر نقراً مزدوجاً على: DIRECT-RUN.bat

2. ✅ الحل الثاني:
   انقر نقراً مزدوجاً على: SIMPLE-START.bat

3. ✅ الحل الثالث:
   انقر نقراً مزدوجاً على: LAUNCH.bat

4. ✅ الحل الرابع (PowerShell):
   انقر بالزر الأيمن على: Start-TrucksManagement.ps1
   واختر "Run with PowerShell"

5. ✅ الحل اليدوي:
   - افتح Command Prompt في هذا المجلد
   - اكتب: npm install electron
   - اكتب: npx electron resources\app

📋 نصائح مهمة:

• تأكد من وجود اتصال إنترنت
• جرب تشغيل الملف كمدير (Run as Administrator)
• تحقق من شريط المهام - قد يكون التطبيق مفتوح
• انتظر 2-3 دقائق في المرة الأولى

⚠️ إذا لم يعمل أي حل:

1. تأكد من تثبيت Node.js:
   - اذهب إلى: https://nodejs.org/
   - حمل وثبت Node.js
   - أعد تشغيل الكمبيوتر
   - جرب مرة أخرى

2. جرب الطريقة اليدوية:
   - افتح Command Prompt كمدير
   - اذهب إلى مجلد التطبيق
   - اكتب: npm install electron
   - اكتب: node_modules\.bin\electron.cmd resources\app

========================================
    Troubleshooting - App Won't Start
========================================

🔧 Problem: CMD closes and app doesn't start

🚀 Solutions (try in order):

1. ✅ First Solution (Best):
   Double-click: DIRECT-RUN.bat

2. ✅ Second Solution:
   Double-click: SIMPLE-START.bat

3. ✅ Third Solution:
   Double-click: LAUNCH.bat

4. ✅ Fourth Solution (PowerShell):
   Right-click: Start-TrucksManagement.ps1
   Select "Run with PowerShell"

5. ✅ Manual Solution:
   - Open Command Prompt in this folder
   - Type: npm install electron
   - Type: npx electron resources\app

📋 Important Tips:

• Make sure you have internet connection
• Try running as Administrator
• Check taskbar - app might be open
• Wait 2-3 minutes on first run

⚠️ If nothing works:

1. Install Node.js:
   - Go to: https://nodejs.org/
   - Download and install Node.js
   - Restart computer
   - Try again

2. Try manual method:
   - Open Command Prompt as Administrator
   - Navigate to app folder
   - Type: npm install electron
   - Type: node_modules\.bin\electron.cmd resources\app
