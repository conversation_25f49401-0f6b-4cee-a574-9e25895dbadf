@echo off
echo ========================================
echo اختبار نظام إدارة أنواع المصروفات
echo ========================================
echo.

echo تشغيل التطبيق...
start "" "TrucksManagement.exe"

echo.
echo ========================================
echo تعليمات الاختبار:
echo ========================================
echo.
echo 1. انتقل إلى صفحة المصروفات
echo 2. تحقق من وجود قسم "أنواع المصروفات" في الأعلى
echo 3. تحقق من عرض الأنواع الافتراضية (وقود، صيانة، زيوت، إلخ)
echo 4. اختبر إضافة نوع مصروف جديد:
echo    - اضغط على زر "إضافة نوع جديد"
echo    - أدخل اسم النوع (مثل: "رسوم طريق")
echo    - أدخل رمز (مثل: "🛣️")
echo    - أدخل وصف (مثل: "رسوم الطرق السريعة")
echo    - اضغط "إضافة النوع"
echo 5. اختبر تعديل نوع موجود:
echo    - اضغط على زر التعديل (✏️) لأي نوع
echo    - غير الاسم أو الوصف
echo    - اضغط "تحديث النوع"
echo 6. تحقق من ظهور النوع الجديد في قائمة إضافة المصروف
echo 7. أضف مصروف جديد باستخدام النوع المضاف
echo 8. اختبر حذف نوع غير مستخدم:
echo    - اضغط على زر الحذف (🗑️) لنوع غير مستخدم
echo 9. حاول حذف نوع مستخدم (يجب أن يظهر تحذير)
echo.
echo ========================================
echo النتائج المتوقعة:
echo ========================================
echo.
echo ✅ عرض أنواع المصروفات في شكل بطاقات منظمة
echo ✅ إضافة أنواع جديدة بنجاح
echo ✅ تعديل الأنواع الموجودة
echo ✅ ظهور الأنواع في قائمة إضافة المصروف
echo ✅ منع حذف الأنواع المستخدمة
echo ✅ حفظ البيانات في localStorage وقاعدة البيانات
echo ✅ تحديث العرض فوري بعد كل عملية
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
