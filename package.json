{"name": "trucks-management-system", "version": "1.0.0", "description": "نظام إدارة شاحنات النقل الثقيل", "main": "dist/main.js", "scripts": {"dev": "npm run build:main && npm run dev:renderer", "dev:renderer": "vite", "dev:main": "npm run build:main && electron dist/main.js", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "start": "electron dist/main.js", "rebuild": "npx electron-rebuild", "pack": "npm run build && electron-builder --dir", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win"}, "keywords": ["electron", "trucks", "management", "sqlite"], "author": "Trucks Management System", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0"}, "build": {"appId": "com.trucksmanagement.app", "productName": "نظام إدارة الشاحنات", "directories": {"output": "release", "buildResources": "assets"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/truck-icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة الشاحنات", "installerIcon": "assets/truck-icon.ico", "uninstallerIcon": "assets/truck-icon.ico", "installerHeaderIcon": "assets/truck-icon.ico", "include": "installer.nsh"}, "mac": {"category": "public.app-category.business", "icon": "assets/truck-icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/truck-icon.png"}, "nodeGypRebuild": false, "buildDependenciesFromSource": false}}