import React, { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, Search, Filter } from 'lucide-react'
import ExpenseForm from '../components/ExpenseForm'

interface Expense {
  id?: number
  truck_id: number
  expense_date: string
  expense_type: 'fuel' | 'maintenance' | 'oil_change' | 'driver_salary' | 'daily_allowance' | 'other'
  amount: number
  description?: string
  driver_name?: string
  attachment_url?: string
  notes?: string
  plate_number?: string
}

interface Truck {
  id: number
  plate_number: string
  driver_name?: string
  status: string
}

const ExpensesPage: React.FC = () => {
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [trucks, setTrucks] = useState<Truck[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTruck, setSelectedTruck] = useState<string>('')
  const [selectedType, setSelectedType] = useState<string>('')
  const [dateFilter, setDateFilter] = useState({
    start: '',
    end: ''
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [expensesData, trucksData] = await Promise.all([
        window.electronAPI.getExpenses(),
        window.electronAPI.getTrucks()
      ])
      setExpenses(expensesData)
      setTrucks(trucksData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddExpense = () => {
    setEditingExpense(null)
    setShowForm(true)
  }

  const handleEditExpense = (expense: Expense) => {
    setEditingExpense(expense)
    setShowForm(true)
  }

  const handleDeleteExpense = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
      try {
        await window.electronAPI.deleteExpense(id)
        await loadData()
      } catch (error) {
        console.error('Error deleting expense:', error)
        alert('حدث خطأ أثناء حذف المصروف')
      }
    }
  }

  const handleFormSubmit = async (expenseData: Omit<Expense, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      if (editingExpense) {
        await window.electronAPI.updateExpense(editingExpense.id!, expenseData)
      } else {
        await window.electronAPI.addExpense(expenseData)
      }
      await loadData()
      setShowForm(false)
      setEditingExpense(null)
    } catch (error) {
      console.error('Error saving expense:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const getExpenseTypeLabel = (type: string) => {
    const types = {
      fuel: 'وقود',
      maintenance: 'صيانة',
      oil_change: 'غيار زيت',
      driver_salary: 'راتب سائق',
      daily_allowance: 'مصاريف يومية',
      other: 'أخرى'
    }
    return types[type as keyof typeof types] || type
  }

  const getExpenseTypeBadge = (type: string) => {
    const typeConfig = {
      fuel: { className: 'bg-red-100 text-red-800' },
      maintenance: { className: 'bg-yellow-100 text-yellow-800' },
      oil_change: { className: 'bg-blue-100 text-blue-800' },
      driver_salary: { className: 'bg-green-100 text-green-800' },
      daily_allowance: { className: 'bg-purple-100 text-purple-800' },
      other: { className: 'bg-gray-100 text-gray-800' }
    }
    
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.other
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
        {getExpenseTypeLabel(type)}
      </span>
    )
  }

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = 
      expense.plate_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      expense.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      expense.driver_name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTruck = !selectedTruck || expense.truck_id.toString() === selectedTruck
    const matchesType = !selectedType || expense.expense_type === selectedType

    const matchesDate = 
      (!dateFilter.start || expense.expense_date >= dateFilter.start) &&
      (!dateFilter.end || expense.expense_date <= dateFilter.end)

    return matchesSearch && matchesTruck && matchesType && matchesDate
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">إدارة المصروفات</h1>
        <button
          onClick={handleAddExpense}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          إضافة مصروف جديد
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="البحث..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pr-10"
              />
            </div>
            
            <select
              value={selectedTruck}
              onChange={(e) => setSelectedTruck(e.target.value)}
              className="input"
            >
              <option value="">جميع الشاحنات</option>
              {trucks.map(truck => (
                <option key={truck.id} value={truck.id}>
                  {truck.plate_number} - {truck.driver_name || 'بدون سائق'}
                </option>
              ))}
            </select>

            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="input"
            >
              <option value="">جميع الأنواع</option>
              <option value="fuel">وقود</option>
              <option value="maintenance">صيانة</option>
              <option value="oil_change">غيار زيت</option>
              <option value="driver_salary">راتب سائق</option>
              <option value="daily_allowance">مصاريف يومية</option>
              <option value="other">أخرى</option>
            </select>

            <input
              type="date"
              placeholder="من تاريخ"
              value={dateFilter.start}
              onChange={(e) => setDateFilter(prev => ({ ...prev, start: e.target.value }))}
              className="input"
            />

            <input
              type="date"
              placeholder="إلى تاريخ"
              value={dateFilter.end}
              onChange={(e) => setDateFilter(prev => ({ ...prev, end: e.target.value }))}
              className="input"
            />
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{filteredExpenses.length}</p>
              <p className="text-sm text-gray-600">إجمالي المصروفات</p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0))}
              </p>
              <p className="text-sm text-gray-600">إجمالي المبلغ</p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {filteredExpenses.length > 0 
                  ? formatCurrency(filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0) / filteredExpenses.length)
                  : formatCurrency(0)
                }
              </p>
              <p className="text-sm text-gray-600">متوسط المصروف</p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(filteredExpenses.filter(e => e.expense_type === 'fuel').reduce((sum, expense) => sum + expense.amount, 0))}
              </p>
              <p className="text-sm text-gray-600">مصروفات الوقود</p>
            </div>
          </div>
        </div>
      </div>

      {/* Expenses Table */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الشاحنة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  نوع المصروف
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المبلغ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الوصف
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  السائق
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredExpenses.map((expense) => (
                <tr key={expense.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(expense.expense_date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {expense.plate_number}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getExpenseTypeBadge(expense.expense_type)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                    {formatCurrency(expense.amount)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {expense.description || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {expense.driver_name || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEditExpense(expense)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteExpense(expense.id!)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredExpenses.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد مصروفات مسجلة</p>
            </div>
          )}
        </div>
      </div>

      {/* Expense Form Modal */}
      {showForm && (
        <ExpenseForm
          expense={editingExpense}
          trucks={trucks}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false)
            setEditingExpense(null)
          }}
        />
      )}
    </div>
  )
}

export default ExpensesPage
