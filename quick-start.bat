@echo off
echo Starting Trucks Management System...
echo.

cd /d "C:\TrucksProject"

echo Step 1: Cleaning old files...
if exist "dist" rmdir /s /q dist

echo Step 2: Creating directories...
mkdir dist
mkdir dist\database

echo Step 3: Building TypeScript files...
npx tsc electron/main.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node
npx tsc electron/preload.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node  
npx tsc electron/database/MockDatabaseManager.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node

echo Step 4: Starting application...
npm start

pause
