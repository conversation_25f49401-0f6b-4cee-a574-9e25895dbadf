@echo off
echo.
echo ========================================
echo 🔧 اختبار إصلاح إغلاق نافذة الرحلات
echo ========================================
echo.
echo 🐛 المشكلة المُبلغ عنها:
echo    - عند تسجيل رحلة جديدة
echo    - البيانات تُحفظ بنجاح
echo    - لكن نافذة الإدخال لا تختفي
echo    - الزر يبقى معطل
echo.
echo ✅ الإصلاح المطبق:
echo    ✅ إغلاق النافذة تلقائياً بعد الحفظ
echo    ✅ مسح جميع الحقول
echo    ✅ إعادة تفعيل الزر
echo    ✅ رسائل تشخيصية في الكونسول
echo.
echo 📋 خطة الاختبار:
echo.
echo 1️⃣  سيتم تشغيل التطبيق
echo 2️⃣  إضافة شاحنة للاختبار
echo 3️⃣  تسجيل رحلة جديدة
echo 4️⃣  التحقق من إغلاق النافذة
echo.
pause

echo 📱 تشغيل التطبيق...
start "" npx electron resources/app

echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 🔍 تعليمات الاختبار المفصلة:
echo.
echo ==========================================
echo 🚛 المرحلة الأولى: إضافة شاحنة للاختبار
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر على تبويب "الشاحنات"
echo    2. انقر "إضافة شاحنة جديدة"
echo    3. املأ البيانات:
echo       - رقم اللوحة: تست 3030
echo       - اسم السائق: سائق الرحلات
echo       - الحالة: نشطة
echo    4. انقر "إضافة"
echo    5. تحقق من إغلاق النافذة وظهور الشاحنة
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ الشاحنة تُضاف وتظهر في القائمة
echo    ✅ النافذة تُغلق تلقائياً
echo    ✅ رسالة نجاح تظهر
echo.
echo ==========================================
echo 🚚 المرحلة الثانية: اختبار تسجيل الرحلة
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر على تبويب "الرحلات"
echo    2. انقر "تسجيل رحلة جديدة"
echo    3. املأ البيانات:
echo       - الشاحنة: تست 3030 - سائق الرحلات
echo       - نوع المادة: رمل
echo       - الكمية: 15
echo       - السعر: 60
echo       - موقع التحميل: محجر الاختبار
echo       - موقع التفريغ: مشروع الاختبار
echo    4. انقر "تسجيل الرحلة"
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ رسالة "جاري الحفظ..." تظهر على الزر
echo    ✅ رسالة نجاح تظهر
echo    ✅ النافذة تُغلق تلقائياً
echo    ✅ الرحلة تظهر في قائمة الرحلات
echo    ✅ جميع الحقول تُمسح
echo.
echo ==========================================
echo 🔄 المرحلة الثالثة: اختبار التكرار
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انقر "تسجيل رحلة جديدة" مرة أخرى
echo    2. تحقق من أن النافذة فارغة ونظيفة
echo    3. املأ البيانات مرة أخرى:
echo       - الشاحنة: تست 3030 - سائق الرحلات
echo       - نوع المادة: حصى
echo       - الكمية: 20
echo       - السعر: 55
echo       - موقع التحميل: محجر ثاني
echo       - موقع التفريغ: مشروع ثاني
echo    4. انقر "تسجيل الرحلة"
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ النافذة فارغة عند الفتح
echo    ✅ الزر نشط وجاهز
echo    ✅ النافذة تُغلق بعد الحفظ
echo    ✅ الرحلة الثانية تُضاف بنجاح
echo.
echo ==========================================
echo 🧪 اختبارات إضافية
echo ==========================================
echo.
echo 🔬 اختبار الإلغاء:
echo    1. افتح نافذة "تسجيل رحلة جديدة"
echo    2. املأ بعض البيانات
echo    3. انقر "إلغاء" أو اضغط Escape
echo    4. تحقق من إغلاق النافذة بدون حفظ
echo.
echo 🔬 اختبار الحقول المطلوبة:
echo    1. افتح نافذة "تسجيل رحلة جديدة"
echo    2. اترك بعض الحقول فارغة
echo    3. انقر "تسجيل الرحلة"
echo    4. تحقق من ظهور رسالة خطأ
echo    5. تحقق من بقاء النافذة مفتوحة
echo    6. تحقق من إعادة تفعيل الزر
echo.
echo 🔬 اختبار الكونسول:
echo    1. اضغط F12 لفتح أدوات المطور
echo    2. انتقل لتبويب "Console"
echo    3. سجل رحلة جديدة
echo    4. راقب الرسائل في الكونسول:
echo       - "🚚 بدء عملية إضافة رحلة..."
echo       - "📝 البيانات المدخلة: ..."
echo       - "💾 تم حفظ الرحلة في قاعدة البيانات"
echo       - "🎯 الآن سيتم إغلاق النافذة..."
echo       - "🔄 محاولة إغلاق النافذة..."
echo       - "✅ تم إخفاء النافذة مباشرة"
echo.
echo ==========================================
echo 📊 تقييم النتائج
echo ==========================================
echo.
echo ✅ الاختبار ناجح إذا:
echo    - النافذة تُغلق تلقائياً بعد الحفظ
echo    - جميع الحقول تُمسح بعد الحفظ
echo    - الزر يُعاد تفعيله بعد الحفظ
echo    - الرحلات تُحفظ وتظهر في القائمة
echo    - رسائل النجاح تظهر
echo    - لا توجد أخطاء في الكونسول
echo.
echo ❌ الاختبار فاشل إذا:
echo    - النافذة تبقى مفتوحة بعد الحفظ
echo    - الحقول لا تُمسح
echo    - الزر يبقى معطل
echo    - الرحلات لا تُحفظ
echo    - ظهور أخطاء في الكونسول
echo.
echo 🎉 إذا نجحت جميع الاختبارات:
echo    - مشكلة إغلاق النافذة محلولة بالكامل
echo    - تجربة مستخدم سلسة ومتكاملة
echo    - النظام جاهز للاستخدام الطبيعي
echo.
echo 💡 ملاحظة مهمة:
echo    إذا لم تُغلق النافذة، تحقق من:
echo    - وجود رسائل خطأ في الكونسول (F12)
echo    - تم ملء جميع الحقول المطلوبة
echo    - الشاحنة موجودة في القائمة
echo    - لا توجد مشاكل في قاعدة البيانات
echo.
pause
