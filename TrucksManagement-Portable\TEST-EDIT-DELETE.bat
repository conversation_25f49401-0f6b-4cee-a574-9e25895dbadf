@echo off
echo.
echo ========================================
echo 🧪 اختبار ميزات التعديل والحذف
echo ========================================
echo.
echo 🆕 الميزات الجديدة:
echo    ✅ تعديل بيانات الشاحنات
echo    ✅ حذف الشاحنات
echo    ✅ أزرار في كل شاحنة
echo    ✅ نافذة تعديل منفصلة
echo    ✅ تأكيد الحذف
echo.
echo 📋 خطوات الاختبار:
echo.
echo 1️⃣  سيتم تشغيل التطبيق
echo 2️⃣  انتقل لصفحة الشاحنات
echo 3️⃣  اختبر التعديل والحذف
echo.
pause

echo 📱 تشغيل التطبيق...
start "" npx electron resources/app

echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 🔍 تعليمات الاختبار المفصلة:
echo.
echo 📊 انتقل لصفحة الشاحنات:
echo    - انقر على تبويب "الشاحنات" في الشريط العلوي
echo    - ستجد قائمة بالشاحنات الموجودة
echo    - كل شاحنة لها زرين: ✏️ (تعديل) و 🗑️ (حذف)
echo.
echo ✏️ اختبار التعديل:
echo    1. انقر على زر التعديل ✏️ بجانب أي شاحنة
echo    2. ستفتح نافذة "تعديل بيانات الشاحنة"
echo    3. ستجد الحقول مملوءة بالبيانات الحالية
echo    4. غير اسم السائق مثلاً إلى "سائق محدث"
echo    5. انقر "حفظ التعديلات"
echo    6. يجب أن تُغلق النافذة تلقائياً
echo    7. تحقق من ظهور الاسم الجديد في القائمة
echo.
echo 🗑️ اختبار الحذف:
echo    1. انقر على زر الحذف 🗑️ بجانب أي شاحنة
echo    2. ستظهر رسالة تأكيد تحتوي على:
echo       - اسم الشاحنة والسائق
echo       - تحذير أن العملية لا يمكن التراجع عنها
echo    3. انقر "موافق" للتأكيد
echo    4. يجب أن تختفي الشاحنة من القائمة فوراً
echo    5. تحقق من تحديث العدادات في الأعلى
echo.
echo 🎯 النتائج المتوقعة:
echo.
echo ✅ للتعديل:
echo    - نافذة التعديل تفتح مع البيانات الحالية
echo    - التعديلات تُحفظ وتظهر في القائمة
echo    - النافذة تُغلق تلقائياً بعد الحفظ
echo    - رسالة نجاح تظهر
echo.
echo ✅ للحذف:
echo    - رسالة تأكيد واضحة تظهر
echo    - الشاحنة تختفي بعد التأكيد
echo    - العدادات تتحدث (عدد الشاحنات)
echo    - رسالة نجاح تظهر
echo.
echo 🐛 إذا لم تعمل:
echo    - افتح Developer Tools (F12)
echo    - راقب رسائل الخطأ في الكونسول
echo    - تأكد من ملء جميع الحقول المطلوبة
echo.
echo 💡 نصائح للاختبار:
echo    1. جرب تعديل شاحنة موجودة أولاً
echo    2. ثم جرب حذف شاحنة غير مهمة
echo    3. أضف شاحنة جديدة واحذفها للتأكد
echo    4. جرب إلغاء عملية الحذف
echo.
echo 📋 تقرير النتائج:
echo    بعد الاختبار، تحقق من:
echo    ✅ هل تعمل أزرار التعديل والحذف؟
echo    ✅ هل تفتح نافذة التعديل بالبيانات الصحيحة؟
echo    ✅ هل تُحفظ التعديلات وتظهر في القائمة؟
echo    ✅ هل تظهر رسالة تأكيد الحذف؟
echo    ✅ هل تختفي الشاحنة بعد الحذف؟
echo.
pause
