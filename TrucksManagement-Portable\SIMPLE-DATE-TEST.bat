@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔍 اختبار بسيط لحقل التاريخ
echo ========================================
echo.
echo 🎯 هدف الاختبار: التأكد من حفظ التاريخ المختار
echo.
echo ========================================
echo    🚀 خطوات الاختبار
echo ========================================
echo.
echo 1️⃣ شغل التطبيق:
echo    └── استخدم DIRECT-RUN.bat
echo.
echo 2️⃣ افتح Developer Tools:
echo    ├── اضغط F12
echo    ├── اذهب لتبويب Console
echo    └── امسح الرسائل
echo.
echo 3️⃣ امسح البيانات القديمة:
echo    ├── اذهب لتبويب Application
echo    ├── Local Storage → امسح الكل
echo    └── أعد تحميل الصفحة (F5)
echo.
echo 4️⃣ سجل رحلة بتاريخ مختلف:
echo    ├── اذهب لصفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── غير التاريخ لـ 2025-06-01
echo    ├── املأ البيانات الأساسية
echo    └── اضغط "تسجيل الرحلة"
echo.
echo 5️⃣ تحقق من النتيجة:
echo    ├── انظر لقائمة الرحلات
echo    ├── هل يظهر التاريخ 2025-06-01؟
echo    └── راجع رسائل Console
echo.
echo ========================================
echo    ✅ النتيجة المتوقعة
echo ========================================
echo.
echo يجب أن ترى في Console:
echo    ├── "📅 التاريخ المختار: 2025-06-01"
echo    ├── "🆕 الرحلة الجديدة: {date: '2025-06-01'}"
echo    └── "📅 عرض رحلة: X تاريخ: 2025-06-01"
echo.
echo ويجب أن ترى في قائمة الرحلات:
echo    └── "رحلة [اسم الشاحنة] - 2025-06-01"
echo.
echo ========================================
echo    ❌ إذا لم تعمل
echo ========================================
echo.
echo تحقق من:
echo    ├── وجود حقل التاريخ في النموذج
echo    ├── عدم وجود أخطاء JavaScript
echo    ├── حفظ البيانات في localStorage
echo    └── تحديث العرض بشكل صحيح
echo.
pause
