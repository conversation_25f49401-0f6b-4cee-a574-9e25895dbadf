<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الألوان</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            color: #333;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            overflow: hidden;
            color: #333 !important;
        }
        
        .report-table th {
            background: #667eea;
            color: white !important;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .report-table td {
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid #ddd;
            color: #333 !important;
            background: white !important;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🎨 اختبار الألوان</h1>
    
    <div class="test-container">
        <h2>اختبار النص العادي</h2>
        <p>هذا نص عادي يجب أن يظهر باللون الأسود</p>
        
        <table class="report-table">
            <thead>
                <tr>
                    <th>الشاحنة</th>
                    <th>التاريخ</th>
                    <th>النوع</th>
                    <th>المبلغ</th>
                    <th>الوصف</th>
                </tr>
            </thead>
            <tbody>
                <tr style="background: white !important;">
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">أ ب ج 1234</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">2024-12-15</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">وقود</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">800 ريال</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">تعبئة وقود كاملة</td>
                </tr>
                <tr style="background: white !important;">
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">ز ح ط 9012</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">2024-12-10</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">صيانة</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">500 ريال</td>
                    <td style="color: #333 !important; background: white !important; font-weight: bold;">تغيير الإطارات الأمامية</td>
                </tr>
                <tr style="background: rgba(103, 126, 234, 0.2); font-weight: bold;">
                    <td colspan="3" style="color: #333 !important;">الإجمالي</td>
                    <td style="color: #333 !important;">1,300 ريال</td>
                    <td style="color: #333 !important;">-</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div style="background: white; padding: 20px; border-radius: 10px; color: #333;">
        <h2>اختبار آخر</h2>
        <p>هذا نص في div أبيض</p>
        
        <table style="width: 100%; border-collapse: collapse; color: #333;">
            <tr style="background: #667eea;">
                <th style="color: white; padding: 10px;">العمود 1</th>
                <th style="color: white; padding: 10px;">العمود 2</th>
            </tr>
            <tr style="background: white;">
                <td style="color: #333; padding: 10px; font-weight: bold;">البيانات 1</td>
                <td style="color: #333; padding: 10px; font-weight: bold;">البيانات 2</td>
            </tr>
        </table>
    </div>
    
    <script>
        console.log('تم تحميل صفحة الاختبار');
        
        // اختبار إضافة بيانات ديناميكياً
        setTimeout(() => {
            const testDiv = document.createElement('div');
            testDiv.style.background = 'white';
            testDiv.style.padding = '20px';
            testDiv.style.borderRadius = '10px';
            testDiv.style.color = '#333';
            testDiv.style.margin = '20px 0';
            
            testDiv.innerHTML = `
                <h3>اختبار ديناميكي</h3>
                <table style="width: 100%; border-collapse: collapse; color: #333 !important;">
                    <tr style="background: #667eea;">
                        <th style="color: white !important; padding: 10px;">الشاحنة</th>
                        <th style="color: white !important; padding: 10px;">المبلغ</th>
                    </tr>
                    <tr style="background: white !important;">
                        <td style="color: #333 !important; padding: 10px; font-weight: bold;">د هـ و 5678</td>
                        <td style="color: #333 !important; padding: 10px; font-weight: bold;">750 ريال</td>
                    </tr>
                </table>
            `;
            
            document.body.appendChild(testDiv);
        }, 1000);
    </script>
</body>
</html>
