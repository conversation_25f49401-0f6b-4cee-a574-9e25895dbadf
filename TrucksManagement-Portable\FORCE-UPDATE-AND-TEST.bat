@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔧 إجبار التحديث واختبار الإصلاح
echo ========================================
echo.
echo 🎯 المشكلة: خطأ updateStats ما زال يحدث
echo 🔧 السبب المحتمل: cache أو نسخة قديمة من الملف
echo ✅ الحل: إجبار التحديث وإعادة التشغيل
echo.
echo ========================================
echo    🔄 خطوات الإجبار
echo ========================================
echo.
echo 1️⃣ نسخ الملف المُحدث:

copy "resources\app\dist\renderer\trucks-app.html" "resources\app\trucks-app.html" >nul

if %errorlevel% == 0 (
    echo ✅ تم نسخ الملف المُحدث
) else (
    echo ❌ فشل في نسخ الملف
)

echo.
echo 2️⃣ إنشاء نسخة احتياطية:

copy "resources\app\dist\renderer\trucks-app.html" "trucks-app-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%.html" >nul

echo ✅ تم إنشاء نسخة احتياطية

echo.
echo ========================================
echo    🚀 اختبار الإصلاح
echo ========================================
echo.
echo 3️⃣ أغلق التطبيق تماماً:
echo    ├── أغلق جميع نوافذ التطبيق
echo    ├── افتح Task Manager (Ctrl+Shift+Esc)
echo    ├── ابحث عن عمليات "Electron" أو "TrucksManagement"
echo    ├── أنهِ جميع العمليات
echo    └── انتظر 10 ثوانِ
echo.
echo 4️⃣ شغل التطبيق من جديد:
echo    └── استخدم DIRECT-RUN.bat
echo.
echo 5️⃣ افتح Developer Tools فوراً:
echo    ├── اضغط F12 أو Ctrl+Shift+I
echo    ├── اذهب لتبويب Console
echo    ├── امسح الرسائل (Clear Console)
echo    └── اتركه مفتوحاً للمراقبة
echo.
echo 6️⃣ اختبر إضافة رحلة:
echo    ├── اذهب لصفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── غير التاريخ لـ 2025-06-01
echo    ├── املأ باقي البيانات
echo    ├── اضغط "تسجيل الرحلة"
echo    └── راقب Console بعناية
echo.
echo ========================================
echo    🔍 الرسائل المتوقعة الآن
echo ========================================
echo.
echo يجب أن ترى في Console:
echo.
echo 📊 رسائل updateStats الجديدة:
echo    ├── "📊 تحديث الإحصائيات..."
echo    ├── "📍 الصفحة الحالية: trips-page"
echo    ├── "🏠 هل هي الصفحة الرئيسية؟ false"
echo    ├── "ℹ️ تخطي تحديث الإحصائيات - ليس في الصفحة الرئيسية"
echo    └── "✅ تم تحديث الإحصائيات بنجاح"
echo.
echo 📅 رسائل التاريخ:
echo    ├── "📅 التاريخ المختار: 2025-06-01"
echo    ├── "📅 نوع التاريخ: string"
echo    ├── "🆕 الرحلة الجديدة: {date: '2025-06-01'}"
echo    └── "📅 عرض رحلة: X تاريخ: 2025-06-01"
echo.
echo ========================================
echo    ✅ النتائج المتوقعة
echo ========================================
echo.
echo 🎉 يجب ألا ترى:
echo    ├── خطأ "Cannot set properties of null"
echo    ├── أي أخطاء JavaScript أخرى
echo    ├── مشاكل في حفظ الرحلة
echo    └── مشاكل في عرض التاريخ
echo.
echo 🎯 يجب أن ترى:
echo    ├── حفظ الرحلة بنجاح
echo    ├── التاريخ المختار يظهر في القائمة
echo    ├── إغلاق نافذة التسجيل تلقائياً
echo    ├── تحديث القوائم بدون أخطاء
echo    └── رسائل تشخيص واضحة
echo.
echo ========================================
echo    💡 إذا استمرت المشكلة
echo ========================================
echo.
echo 🔍 تحقق من:
echo    ├── أن التطبيق يستخدم الملف الصحيح
echo    ├── عدم وجود cache في المتصفح
echo    ├── أن جميع العمليات مُغلقة
echo    └── أن الملف تم تحديثه فعلاً
echo.
echo 🛠️ حلول إضافية:
echo    ├── جرب Ctrl+Shift+R للتحديث القسري
echo    ├── امسح localStorage من Developer Tools
echo    ├── أعد تشغيل الكمبيوتر إذا لزم الأمر
echo    └── تحقق من أن main.js يشير للملف الصحيح
echo.
pause
