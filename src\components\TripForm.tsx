import React, { useEffect } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { X } from 'lucide-react'

interface Trip {
  id?: number
  truck_id: number
  trip_date: string
  serial_number?: string
  material_type: string
  quantity_ton: number
  price_per_ton: number
  subtotal: number
  tax: number
  total: number
  loading_location: string
  unloading_location: string
  notes?: string
}

interface Truck {
  id: number
  plate_number: string
  driver_name?: string
  status: string
}

interface TripFormProps {
  trip?: Trip | null
  trucks: Truck[]
  onSubmit: (data: Omit<Trip, 'id' | 'created_at' | 'updated_at'>) => void
  onCancel: () => void
}

const TripForm: React.FC<TripFormProps> = ({ trip, trucks, onSubmit, onCancel }) => {
  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<Omit<Trip, 'id' | 'created_at' | 'updated_at'>>({
    defaultValues: {
      truck_id: trip?.truck_id || 0,
      trip_date: trip?.trip_date || '', // لا نعيد تعيين التاريخ - نتركه فارغاً للمستخدم
      serial_number: trip?.serial_number || '',
      material_type: trip?.material_type || '',
      quantity_ton: trip?.quantity_ton || 0,
      price_per_ton: trip?.price_per_ton || 0,
      subtotal: trip?.subtotal || 0,
      tax: trip?.tax || 0,
      total: trip?.total || 0,
      loading_location: trip?.loading_location || '',
      unloading_location: trip?.unloading_location || '',
      notes: trip?.notes || ''
    }
  })

  // Watch for changes in quantity and price to calculate totals
  const quantity = useWatch({ control, name: 'quantity_ton' })
  const pricePerTon = useWatch({ control, name: 'price_per_ton' })
  const tax = useWatch({ control, name: 'tax' })

  useEffect(() => {
    if (quantity && pricePerTon) {
      const subtotal = quantity * pricePerTon
      const total = subtotal + (tax || 0)
      
      setValue('subtotal', subtotal)
      setValue('total', total)
    }
  }, [quantity, pricePerTon, tax, setValue])

  const handleFormSubmit = (data: Omit<Trip, 'id' | 'created_at' | 'updated_at'>) => {
    onSubmit(data)
  }

  const materialTypes = [
    '3/4',
    'Base Course',
    'Sub Base',
    'رمل',
    'حصى',
    'خرسانة',
    'أسفلت',
    'تراب',
    'أخرى'
  ]

  const commonLocations = [
    'الرياض',
    'جدة',
    'الدمام',
    'مكة المكرمة',
    'المدينة المنورة',
    'الطائف',
    'تبوك',
    'بريدة',
    'خميس مشيط',
    'حائل'
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {trip ? 'تعديل الرحلة' : 'إضافة رحلة جديدة'}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">معلومات أساسية</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الشاحنة *
                </label>
                <select
                  {...register('truck_id', { 
                    required: 'اختيار الشاحنة مطلوب',
                    valueAsNumber: true 
                  })}
                  className="input"
                >
                  <option value={0}>اختر الشاحنة</option>
                  {trucks.filter(truck => truck.status === 'active').map(truck => (
                    <option key={truck.id} value={truck.id}>
                      {truck.plate_number} - {truck.driver_name || 'بدون سائق'}
                    </option>
                  ))}
                </select>
                {errors.truck_id && (
                  <p className="mt-1 text-sm text-red-600">{errors.truck_id.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  تاريخ الرحلة *
                </label>
                <input
                  type="date"
                  {...register('trip_date', { required: 'تاريخ الرحلة مطلوب' })}
                  className="input"
                />
                {errors.trip_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.trip_date.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الرقم المتسلسل
                </label>
                <input
                  type="text"
                  {...register('serial_number')}
                  className="input"
                  placeholder="رقم متسلسل اختياري"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  نوع المادة *
                </label>
                <select
                  {...register('material_type', { required: 'نوع المادة مطلوب' })}
                  className="input"
                >
                  <option value="">اختر نوع المادة</option>
                  {materialTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                {errors.material_type && (
                  <p className="mt-1 text-sm text-red-600">{errors.material_type.message}</p>
                )}
              </div>
            </div>

            {/* Financial Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">المعلومات المالية</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الكمية (طن) *
                </label>
                <input
                  type="number"
                  step="0.1"
                  {...register('quantity_ton', { 
                    required: 'الكمية مطلوبة',
                    valueAsNumber: true,
                    min: { value: 0.1, message: 'الكمية يجب أن تكون أكبر من 0' }
                  })}
                  className="input"
                  placeholder="0.0"
                />
                {errors.quantity_ton && (
                  <p className="mt-1 text-sm text-red-600">{errors.quantity_ton.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  سعر الطن (ريال) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  {...register('price_per_ton', { 
                    required: 'سعر الطن مطلوب',
                    valueAsNumber: true,
                    min: { value: 0.01, message: 'السعر يجب أن يكون أكبر من 0' }
                  })}
                  className="input"
                  placeholder="0.00"
                />
                {errors.price_per_ton && (
                  <p className="mt-1 text-sm text-red-600">{errors.price_per_ton.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المجموع الفرعي (ريال)
                </label>
                <input
                  type="number"
                  step="0.01"
                  {...register('subtotal', { valueAsNumber: true })}
                  className="input bg-gray-50"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الضريبة (ريال)
                </label>
                <input
                  type="number"
                  step="0.01"
                  {...register('tax', { valueAsNumber: true })}
                  className="input"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الإجمالي شامل الضريبة (ريال)
                </label>
                <input
                  type="number"
                  step="0.01"
                  {...register('total', { valueAsNumber: true })}
                  className="input bg-gray-50"
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">معلومات المواقع</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مكان التحميل *
                </label>
                <input
                  type="text"
                  {...register('loading_location', { required: 'مكان التحميل مطلوب' })}
                  className="input"
                  placeholder="مكان التحميل"
                  list="loading-locations"
                />
                <datalist id="loading-locations">
                  {commonLocations.map(location => (
                    <option key={location} value={location} />
                  ))}
                </datalist>
                {errors.loading_location && (
                  <p className="mt-1 text-sm text-red-600">{errors.loading_location.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مكان التنزيل *
                </label>
                <input
                  type="text"
                  {...register('unloading_location', { required: 'مكان التنزيل مطلوب' })}
                  className="input"
                  placeholder="مكان التنزيل"
                  list="unloading-locations"
                />
                <datalist id="unloading-locations">
                  {commonLocations.map(location => (
                    <option key={location} value={location} />
                  ))}
                </datalist>
                {errors.unloading_location && (
                  <p className="mt-1 text-sm text-red-600">{errors.unloading_location.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ملاحظات إضافية
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="input resize-none"
              placeholder="ملاحظات إضافية (اختياري)"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline"
              disabled={isSubmitting}
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحفظ...' : trip ? 'تحديث الرحلة' : 'إضافة الرحلة'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default TripForm
