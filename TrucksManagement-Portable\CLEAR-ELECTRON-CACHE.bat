@echo off
echo.
echo ========================================
echo    مسح cache الـ Electron
echo ========================================
echo.
echo المشكلة: Electron يستخدم cache قديم
echo الحل: مسح جميع ملفات الـ cache
echo.

echo 1. إغلاق جميع نوافذ Electron...
taskkill /f /im electron.exe 2>nul
taskkill /f /im "نظام إدارة الشاحنات.exe" 2>nul
timeout /t 2 >nul

echo 2. مسح cache الـ Electron...
if exist "%APPDATA%\نظام إدارة الشاحنات" (
    echo مسح مجلد البيانات...
    rmdir /s /q "%APPDATA%\نظام إدارة الشاحنات"
)

if exist "%LOCALAPPDATA%\نظام إدارة الشاحنات" (
    echo مسح مجلد الـ cache المحلي...
    rmdir /s /q "%LOCALAPPDATA%\نظام إدارة الشاحنات"
)

if exist "%TEMP%\electron*" (
    echo مسح ملفات Electron المؤقتة...
    del /q "%TEMP%\electron*" 2>nul
)

echo 3. مسح localStorage محلياً...
if exist "data" (
    echo مسح مجلد البيانات المحلي...
    rmdir /s /q "data"
)

echo.
echo ✅ تم مسح جميع ملفات الـ cache!
echo.
echo الآن شغل التطبيق من جديد:
echo - استخدم DIRECT-RUN.bat
echo - أو انقر على نظام إدارة الشاحنات.bat
echo.
echo يجب أن يعمل التاريخ بشكل صحيح الآن!
echo.
pause
