@echo off
chcp 65001 >nul
echo ========================================
echo    بناء وتشغيل نظام إدارة الشاحنات
echo ========================================
echo.

echo التحقق من المجلد...
cd /d "C:\TrucksProject"
if errorlevel 1 (
    echo خطأ: لا يمكن العثور على مجلد المشروع!
    echo تأكد من وجود المجلد: C:\TrucksProject
    pause
    exit /b 1
)

echo المجلد الحالي: %CD%
echo.

echo الخطوة 1: إنشاء مجلد dist...
if not exist "dist" (
    mkdir dist
    echo تم إنشاء مجلد dist
) else (
    echo مجلد dist موجود
)

echo.
echo الخطوة 2: التحقق من ملفات TypeScript...
if not exist "electron\main.ts" (
    echo خطأ: ملف electron\main.ts غير موجود!
    pause
    exit /b 1
)
if not exist "electron\preload.ts" (
    echo خطأ: ملف electron\preload.ts غير موجود!
    pause
    exit /b 1
)
echo ملفات TypeScript موجودة ✓

echo.
echo الخطوة 3: بناء main.ts...
echo تشغيل: npx tsc electron/main.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule
npx tsc electron/main.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule
if errorlevel 1 (
    echo.
    echo ❌ خطأ في بناء main.ts
    echo تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)
echo ✓ تم بناء main.ts بنجاح

echo.
echo الخطوة 4: بناء preload.ts...
npx tsc electron/preload.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule
if errorlevel 1 (
    echo.
    echo ❌ خطأ في بناء preload.ts
    echo تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)
echo ✓ تم بناء preload.ts بنجاح

echo.
echo الخطوة 5: التحقق من الملفات المبنية...
if exist "dist\main.js" (
    echo ✓ main.js تم إنشاؤه بنجاح
    dir "dist\main.js"
) else (
    echo ❌ فشل في إنشاء main.js
    echo محتويات مجلد dist:
    dir dist
    pause
    exit /b 1
)

if exist "dist\preload.js" (
    echo ✓ preload.js تم إنشاؤه بنجاح
    dir "dist\preload.js"
) else (
    echo ❌ فشل في إنشاء preload.js
    echo محتويات مجلد dist:
    dir dist
    pause
    exit /b 1
)

echo.
echo الخطوة 6: التحقق من Electron...
where electron >nul 2>&1
if errorlevel 1 (
    echo تثبيت Electron محلياً...
    npm install electron --save-dev
)

echo.
echo الخطوة 7: تشغيل التطبيق...
echo سيفتح التطبيق الآن...
echo تشغيل: npx electron dist/main.js
echo.

npx electron dist/main.js
if errorlevel 1 (
    echo.
    echo ❌ خطأ في تشغيل التطبيق
    echo تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo ✓ تم إغلاق التطبيق بنجاح
pause
