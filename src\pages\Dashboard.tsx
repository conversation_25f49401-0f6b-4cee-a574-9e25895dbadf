import React, { useEffect, useState } from 'react'
import { Truck, Route as RouteIcon, Receipt, TrendingUp } from 'lucide-react'

interface DashboardStats {
  totalTrucks: number
  activeTrucks: number
  totalTrips: number
  totalRevenue: number
  totalExpenses: number
  netProfit: number
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalTrucks: 0,
    activeTrucks: 0,
    totalTrips: 0,
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Get trucks data
      const trucks = await window.electronAPI.getTrucks()
      const activeTrucks = trucks.filter(truck => truck.status === 'active')
      
      // Get current month trips
      const currentDate = new Date()
      const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
      const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
      
      const trips = await window.electronAPI.getTrips({
        start_date: firstDay.toISOString().split('T')[0],
        end_date: lastDay.toISOString().split('T')[0]
      })
      
      const expenses = await window.electronAPI.getExpenses({
        start_date: firstDay.toISOString().split('T')[0],
        end_date: lastDay.toISOString().split('T')[0]
      })
      
      const totalRevenue = trips.reduce((sum, trip) => sum + trip.total, 0)
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
      
      setStats({
        totalTrucks: trucks.length,
        activeTrucks: activeTrucks.length,
        totalTrips: trips.length,
        totalRevenue,
        totalExpenses,
        netProfit: totalRevenue - totalExpenses
      })
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const statCards = [
    {
      title: 'إجمالي الشاحنات',
      value: stats.totalTrucks,
      icon: Truck,
      color: 'bg-blue-500',
      textColor: 'text-blue-600'
    },
    {
      title: 'الشاحنات النشطة',
      value: stats.activeTrucks,
      icon: Truck,
      color: 'bg-green-500',
      textColor: 'text-green-600'
    },
    {
      title: 'رحلات هذا الشهر',
      value: stats.totalTrips,
      icon: RouteIcon,
      color: 'bg-purple-500',
      textColor: 'text-purple-600'
    },
    {
      title: 'إيرادات الشهر',
      value: formatCurrency(stats.totalRevenue),
      icon: TrendingUp,
      color: 'bg-emerald-500',
      textColor: 'text-emerald-600'
    },
    {
      title: 'مصروفات الشهر',
      value: formatCurrency(stats.totalExpenses),
      icon: Receipt,
      color: 'bg-red-500',
      textColor: 'text-red-600'
    },
    {
      title: 'صافي الربح',
      value: formatCurrency(stats.netProfit),
      icon: TrendingUp,
      color: stats.netProfit >= 0 ? 'bg-green-500' : 'bg-red-500',
      textColor: stats.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
        <button
          onClick={loadDashboardData}
          className="btn-primary"
        >
          تحديث البيانات
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className={`text-2xl font-bold ${card.textColor}`}>
                    {card.value}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${card.color}`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">نظرة عامة سريعة</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">معدل الربحية</span>
                <span className={`font-semibold ${
                  stats.totalRevenue > 0 
                    ? (stats.netProfit / stats.totalRevenue * 100) >= 20 
                      ? 'text-green-600' 
                      : 'text-yellow-600'
                    : 'text-gray-600'
                }`}>
                  {stats.totalRevenue > 0 
                    ? `${((stats.netProfit / stats.totalRevenue) * 100).toFixed(1)}%`
                    : '0%'
                  }
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">متوسط الإيراد لكل رحلة</span>
                <span className="font-semibold text-blue-600">
                  {stats.totalTrips > 0 
                    ? formatCurrency(stats.totalRevenue / stats.totalTrips)
                    : formatCurrency(0)
                  }
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">متوسط المصروف لكل شاحنة</span>
                <span className="font-semibold text-red-600">
                  {stats.activeTrucks > 0 
                    ? formatCurrency(stats.totalExpenses / stats.activeTrucks)
                    : formatCurrency(0)
                  }
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">الإجراءات السريعة</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-2 gap-4">
              <button className="btn-primary text-center p-4 rounded-lg">
                إضافة رحلة جديدة
              </button>
              <button className="btn-secondary text-center p-4 rounded-lg">
                تسجيل مصروف
              </button>
              <button className="btn-outline text-center p-4 rounded-lg">
                إضافة شاحنة
              </button>
              <button className="btn-ghost text-center p-4 rounded-lg">
                عرض التقارير
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
