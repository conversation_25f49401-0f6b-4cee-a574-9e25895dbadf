@echo off
title Trucks Management System Launcher
color 0A
echo ========================================
echo    Trucks Management System Launcher
echo ========================================
echo.

cd /d "%~dp0"

echo [1/3] Preparing to launch...
echo.

REM Check if electron is already installed locally
if exist "node_modules\.bin\electron.cmd" (
    echo [2/3] Using local Electron installation...
    echo [3/3] Starting application...
    echo.
    start "" "node_modules\.bin\electron.cmd" resources\app
    echo Application started! Check for new window.
    goto :end
)

echo [2/3] First time setup - Installing Electron...
echo This will take 1-3 minutes. Please wait...
echo.

REM Install electron locally first
call npm install electron

if %errorlevel% equ 0 (
    echo.
    echo [3/3] Installation complete! Starting application...
    echo.
    if exist "node_modules\.bin\electron.cmd" (
        start "" "node_modules\.bin\electron.cmd" resources\app
        echo Application started! Check for new window.
    ) else (
        echo Trying alternative method...
        start "" npx electron resources\app
    )
) else (
    echo.
    echo Installation failed. Trying direct method...
    start "" npx electron resources\app
)

:end
echo.
echo ========================================
echo If the application window doesn't appear:
echo 1. Check Windows taskbar
echo 2. Try running as administrator
echo 3. Make sure Node.js is installed
echo ========================================
echo.
pause
