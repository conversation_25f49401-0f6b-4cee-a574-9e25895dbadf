@echo off
echo ========================================
echo    بناء ملف التثبيت - نظام إدارة الشاحنات
echo ========================================
cd /d "C:\TrucksProject"

echo الخطوة 1: تنظيف الملفات القديمة...
if exist "dist" rmdir /s /q dist
if exist "release" rmdir /s /q release

echo الخطوة 2: تثبيت التبعيات...
npm install

echo الخطوة 3: إعادة بناء native modules...
npm run rebuild

echo الخطوة 4: بناء التطبيق...
npm run build

echo الخطوة 5: إنشاء ملف التثبيت...
npm run dist:win

echo.
echo ========================================
echo تم إنشاء ملف التثبيت بنجاح!
echo ستجد الملف في مجلد: release
echo ========================================

pause
explorer release
