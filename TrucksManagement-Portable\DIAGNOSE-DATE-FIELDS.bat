@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔍 تشخيص مشكلة حقول التاريخ
echo ========================================
echo.
echo 🎯 المشكلة: حقول التاريخ لا تظهر رغم وجودها في الكود
echo.
echo 📋 الفحوصات المطلوبة:
echo.
echo 1️⃣ تأكد من إغلاق التطبيق تماماً:
echo    ├── أغلق جميع نوافذ التطبيق
echo    ├── تأكد من عدم وجود عمليات Electron في Task Manager
echo    └── انتظر 10 ثوانِ قبل إعادة التشغيل
echo.
echo 2️⃣ امسح cache المتصفح:
echo    ├── شغل التطبيق
echo    ├── اضغط Ctrl+Shift+I لفتح Developer Tools
echo    ├── اضغط F12 أو اذهب لتبويب Application
echo    ├── اختر Storage من القائمة اليسرى
echo    ├── اضغط "Clear storage" أو امسح Local Storage
echo    └── أعد تحميل الصفحة (Ctrl+R)
echo.
echo 3️⃣ تحقق من الملف المُحمل:
echo    ├── افتح Developer Tools (F12)
echo    ├── اذهب لتبويب Sources أو Network
echo    ├── تأكد من أن الملف المُحمل هو:
echo    └── resources/app/dist/renderer/trucks-app.html
echo.
echo 4️⃣ فحص وجود العناصر في DOM:
echo    ├── افتح Developer Tools (F12)
echo    ├── اذهب لتبويب Console
echo    ├── اكتب: document.getElementById('trip-date')
echo    ├── اضغط Enter
echo    ├── إذا ظهر null = العنصر غير موجود
echo    └── إذا ظهر <input...> = العنصر موجود
echo.
echo 5️⃣ فحص النموذج بالكامل:
echo    ├── افتح نموذج تسجيل رحلة جديدة
echo    ├── افتح Developer Tools (F12)
echo    ├── اذهب لتبويب Elements
echo    ├── ابحث عن add-trip-modal
echo    ├── تحقق من وجود حقل trip-date
echo    └── تحقق من أن النموذج يحتوي على جميع الحقول
echo.
echo 6️⃣ اختبار بديل - إضافة حقل يدوياً:
echo    ├── افتح Developer Tools (F12)
echo    ├── اذهب لتبويب Console
echo    ├── اكتب الكود التالي:
echo    │   var dateField = document.createElement('input');
echo    │   dateField.type = 'date';
echo    │   dateField.id = 'trip-date-test';
echo    │   dateField.value = new Date().toISOString().split('T')[0];
echo    │   document.querySelector('#add-trip-modal .modal-content').appendChild(dateField);
echo    ├── اضغط Enter
echo    └── تحقق من ظهور حقل التاريخ
echo.
echo ========================================
echo    🔧 حلول محتملة
echo ========================================
echo.
echo إذا لم تظهر الحقول بعد الفحوصات:
echo.
echo ✅ الحل 1 - إعادة تشغيل كاملة:
echo    ├── أغلق التطبيق تماماً
echo    ├── أعد تشغيل الكمبيوتر (إذا لزم الأمر)
echo    └── شغل التطبيق من جديد
echo.
echo ✅ الحل 2 - فرض إعادة التحميل:
echo    ├── شغل التطبيق
echo    ├── اضغط Ctrl+Shift+R (Hard Reload)
echo    └── أو اضغط Ctrl+F5
echo.
echo ✅ الحل 3 - تحقق من الملف الصحيح:
echo    ├── تأكد من أن main.js يشير للملف الصحيح
echo    ├── resources/app/dist/renderer/trucks-app.html
echo    └── وليس ملف آخر
echo.
echo ✅ الحل 4 - إعادة بناء التطبيق:
echo    ├── استخدم BUILD-COMMERCIAL.bat
echo    └── أو انسخ الملف المُحدث يدوياً
echo.
echo ========================================
echo    📝 معلومات تقنية
echo ========================================
echo.
echo 🔍 الحقول المُضافة:
echo    ├── trip-date في نموذج الرحلة (السطر 2297)
echo    ├── expense-date في نموذج المصروف (السطر 2337)
echo    ├── edit-trip-date في نموذج تعديل الرحلة
echo    └── edit-expense-date في نموذج تعديل المصروف
echo.
echo 🔧 الدوال المُحدثة:
echo    ├── showAddTripModal() - تعين التاريخ الافتراضي
echo    ├── showAddExpenseModal() - تعين التاريخ الافتراضي
echo    ├── addTrip() - تقرأ التاريخ من الحقل
echo    ├── addExpense() - تقرأ التاريخ من الحقل
echo    ├── editTrip() - تملأ حقل التاريخ
echo    └── updateTrip() - تحدث التاريخ
echo.
echo 💡 إذا استمرت المشكلة:
echo    ├── تحقق من وجود أخطاء JavaScript في Console
echo    ├── تأكد من أن CSS لا يخفي الحقول
echo    └── جرب فتح الملف مباشرة في المتصفح
echo.
pause
