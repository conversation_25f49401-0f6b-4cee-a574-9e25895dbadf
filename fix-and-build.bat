@echo off
echo ========================================
echo    إصلاح المشاكل وبناء التطبيق
echo ========================================
cd /d "C:\TrucksProject"

echo الخطوة 1: تنظيف cache...
npm cache clean --force

echo الخطوة 2: حذف node_modules...
if exist "node_modules" rmdir /s /q node_modules

echo الخطوة 3: حذف package-lock.json...
if exist "package-lock.json" del package-lock.json

echo الخطوة 4: تثبيت التبعيات من جديد...
npm install

echo الخطوة 5: تثبيت electron-rebuild...
npm install -g electron-rebuild

echo الخطوة 6: إعادة بناء native modules...
npx electron-rebuild

echo الخطوة 7: بناء التطبيق...
npm run build

echo الخطوة 8: اختبار التطبيق...
npm start

pause
