<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص التنقل - نظام إدارة الشاحنات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 تشخيص التنقل - نظام إدارة الشاحنات</h1>
            <p>أداة تشخيص للتحقق من عمل التنقل بين الصفحات</p>
        </div>

        <div class="test-section">
            <h3>🎯 اختبار التنقل الأساسي</h3>
            <p>اضغط على الأزرار التالية لاختبار التنقل:</p>
            <button class="test-button" onclick="testNavigation('home')">🏠 الرئيسية</button>
            <button class="test-button" onclick="testNavigation('trucks')">🚛 الشاحنات</button>
            <button class="test-button" onclick="testNavigation('trips')">🛣️ الرحلات</button>
            <button class="test-button" onclick="testNavigation('expenses')">💰 المصروفات</button>
            <button class="test-button" onclick="testNavigation('reports')">📊 التقارير</button>
            <div id="navigation-result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 فحص الصفحات المتاحة</h3>
            <button class="test-button" onclick="checkAvailablePages()">فحص الصفحات</button>
            <div id="pages-result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 اختبار الدوال</h3>
            <button class="test-button" onclick="testFunctions()">اختبار الدوال</button>
            <div id="functions-result"></div>
        </div>

        <div class="test-section">
            <h3>🚀 فتح التطبيق الرئيسي</h3>
            <button class="test-button" onclick="openMainApp()">فتح التطبيق</button>
            <div id="app-result"></div>
        </div>

        <div class="test-section">
            <h3>📋 سجل التشخيص</h3>
            <button class="test-button" onclick="clearConsole()">مسح السجل</button>
            <div id="console-output"></div>
        </div>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function testNavigation(pageId) {
            log(`بدء اختبار التنقل إلى: ${pageId}`);
            
            try {
                // محاولة فتح التطبيق الرئيسي في نافذة جديدة
                const appWindow = window.open('resources/app/dist/renderer/trucks-app.html', '_blank');
                
                if (appWindow) {
                    log(`تم فتح التطبيق بنجاح`, 'success');
                    
                    // انتظار تحميل التطبيق ثم اختبار التنقل
                    setTimeout(() => {
                        try {
                            if (appWindow.showPage && typeof appWindow.showPage === 'function') {
                                appWindow.showPage(pageId);
                                log(`تم استدعاء showPage('${pageId}') بنجاح`, 'success');
                            } else {
                                log(`دالة showPage غير متاحة في التطبيق`, 'error');
                            }
                        } catch (error) {
                            log(`خطأ في استدعاء showPage: ${error.message}`, 'error');
                        }
                    }, 2000);
                    
                    document.getElementById('navigation-result').innerHTML = 
                        `<div class="success">تم فتح التطبيق - تحقق من النافذة الجديدة</div>`;
                } else {
                    log(`فشل في فتح التطبيق`, 'error');
                    document.getElementById('navigation-result').innerHTML = 
                        `<div class="error">فشل في فتح التطبيق - تحقق من مسار الملف</div>`;
                }
            } catch (error) {
                log(`خطأ في اختبار التنقل: ${error.message}`, 'error');
                document.getElementById('navigation-result').innerHTML = 
                    `<div class="error">خطأ: ${error.message}</div>`;
            }
        }

        function checkAvailablePages() {
            log('فحص الصفحات المتاحة...');
            
            const expectedPages = [
                'home-page',
                'trucks-page', 
                'trips-page',
                'expenses-page',
                'reports-page'
            ];
            
            let result = '<div class="info">الصفحات المتوقعة:</div>';
            expectedPages.forEach(pageId => {
                result += `<div>📄 ${pageId}</div>`;
            });
            
            result += '<div class="info">لفحص الصفحات الفعلية، افتح التطبيق الرئيسي واضغط F12 ثم اكتب في Console:</div>';
            result += '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace;">document.querySelectorAll(\'.page\').forEach(p => console.log(p.id));</div>';
            
            document.getElementById('pages-result').innerHTML = result;
            log('تم عرض معلومات الصفحات المتوقعة', 'success');
        }

        function testFunctions() {
            log('اختبار توفر الدوال...');
            
            const requiredFunctions = [
                'showPage',
                'updateStats', 
                'updateFinancialReports',
                'displayTrucks',
                'displayTrips',
                'displayExpenses'
            ];
            
            let result = '<div class="info">الدوال المطلوبة:</div>';
            requiredFunctions.forEach(funcName => {
                result += `<div>⚙️ ${funcName}</div>`;
            });
            
            result += '<div class="info">لاختبار الدوال، افتح التطبيق الرئيسي واضغط F12 ثم اكتب في Console:</div>';
            result += '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace;">typeof showPage</div>';
            
            document.getElementById('functions-result').innerHTML = result;
            log('تم عرض معلومات الدوال المطلوبة', 'success');
        }

        function openMainApp() {
            log('فتح التطبيق الرئيسي...');
            
            try {
                window.open('resources/app/dist/renderer/trucks-app.html', '_blank');
                document.getElementById('app-result').innerHTML = 
                    '<div class="success">تم فتح التطبيق في نافذة جديدة</div>';
                log('تم فتح التطبيق بنجاح', 'success');
            } catch (error) {
                document.getElementById('app-result').innerHTML = 
                    `<div class="error">خطأ في فتح التطبيق: ${error.message}</div>`;
                log(`خطأ في فتح التطبيق: ${error.message}`, 'error');
            }
        }

        function clearConsole() {
            consoleOutput.textContent = '';
            log('تم مسح سجل التشخيص', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('تم تحميل أداة التشخيص بنجاح', 'success');
            log('استخدم الأزرار أعلاه لاختبار التنقل', 'info');
        };
    </script>
</body>
</html>
