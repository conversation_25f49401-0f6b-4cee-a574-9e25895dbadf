import React, { useEffect, useState } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts'
import { Calendar, Download, Filter, TrendingUp, TrendingDown } from 'lucide-react'

interface ReportFilters {
  truck_id?: number
  year?: number
  month?: number
  start_date?: string
  end_date?: string
}

const ReportsPage: React.FC = () => {
  const [trucks, setTrucks] = useState<any[]>([])
  const [monthlyReport, setMonthlyReport] = useState<any[]>([])
  const [profitabilityReport, setProfitabilityReport] = useState<any[]>([])
  const [truckComparison, setTruckComparison] = useState<any[]>([])
  const [materialAnalysis, setMaterialAnalysis] = useState<any[]>([])
  const [locationAnalysis, setLocationAnalysis] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('monthly')
  const [filters, setFilters] = useState<ReportFilters>({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadReports()
  }, [filters, activeTab])

  const loadData = async () => {
    try {
      const trucksData = await window.electronAPI.getTrucks()
      setTrucks(trucksData)
    } catch (error) {
      console.error('Error loading data:', error)
    }
  }

  const loadReports = async () => {
    try {
      setLoading(true)
      
      switch (activeTab) {
        case 'monthly':
          const monthlyData = await window.electronAPI.getReports('monthly', filters)
          setMonthlyReport(monthlyData)
          break
        case 'profitability':
          const profitData = await window.electronAPI.getReports('profitability', filters)
          setProfitabilityReport(profitData)
          break
        case 'comparison':
          const comparisonData = await window.electronAPI.getReports('truck_comparison', filters)
          setTruckComparison(comparisonData)
          break
        case 'materials':
          const materialData = await window.electronAPI.getReports('material_analysis', filters)
          setMaterialAnalysis(materialData)
          break
        case 'locations':
          const locationData = await window.electronAPI.getReports('location_analysis', filters)
          setLocationAnalysis(locationData)
          break
      }
    } catch (error) {
      console.error('Error loading reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const tabs = [
    { id: 'monthly', label: 'التقرير الشهري', icon: Calendar },
    { id: 'profitability', label: 'تقرير الربحية', icon: TrendingUp },
    { id: 'comparison', label: 'مقارنة الشاحنات', icon: BarChart },
    { id: 'materials', label: 'تحليل المواد', icon: PieChart },
    { id: 'locations', label: 'تحليل المواقع', icon: Filter }
  ]

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  const renderMonthlyReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">الإيرادات والمصروفات</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyReport}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="plate_number" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Legend />
                <Bar dataKey="total_revenue" fill="#10B981" name="الإيرادات" />
                <Bar dataKey="total_expenses" fill="#EF4444" name="المصروفات" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">صافي الربح</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyReport}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="plate_number" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Bar dataKey="net_profit" fill="#3B82F6" name="صافي الربح" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">تفاصيل الشاحنات</h3>
        </div>
        <div className="card-content">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الشاحنة</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السائق</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الرحلات</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية الكلية</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإيرادات</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المصروفات</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">صافي الربح</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {monthlyReport.map((truck, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {truck.plate_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {truck.driver_name || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {truck.total_trips}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {truck.total_quantity?.toFixed(1)} طن
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                      {formatCurrency(truck.total_revenue || 0)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                      {formatCurrency(truck.total_expenses || 0)}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                      (truck.net_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(truck.net_profit || 0)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )

  const renderProfitabilityReport = () => (
    <div className="space-y-6">
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">تطور الربحية اليومية</h3>
        </div>
        <div className="card-content">
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={profitabilityReport}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              <Legend />
              <Line type="monotone" dataKey="daily_revenue" stroke="#10B981" name="الإيرادات اليومية" />
              <Line type="monotone" dataKey="daily_expenses" stroke="#EF4444" name="المصروفات اليومية" />
              <Line type="monotone" dataKey="daily_profit" stroke="#3B82F6" name="الربح اليومي" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  )

  const renderTruckComparison = () => (
    <div className="space-y-6">
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">مقارنة أداء الشاحنات</h3>
        </div>
        <div className="card-content">
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={truckComparison}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="plate_number" />
              <YAxis />
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              <Legend />
              <Bar dataKey="net_profit" fill="#3B82F6" name="صافي الربح" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  )

  const renderMaterialAnalysis = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">توزيع الإيرادات حسب نوع المادة</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={materialAnalysis}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="total_revenue"
                >
                  {materialAnalysis.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">تفاصيل المواد</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {materialAnalysis.map((material, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <span className="font-medium">{material.material_type}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-600">{material.trip_count} رحلة</div>
                    <div className="text-sm text-gray-600">{material.total_quantity} طن</div>
                    <div className="font-medium text-green-600">{formatCurrency(material.total_revenue)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderLocationAnalysis = () => (
    <div className="space-y-6">
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">تحليل المواقع</h3>
        </div>
        <div className="card-content">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">مكان التحميل</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">مكان التنزيل</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الرحلات</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية الكلية</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي الإيرادات</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {locationAnalysis.map((location, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {location.loading_location}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {location.unloading_location}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {location.trip_count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {location.total_quantity} طن
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                      {formatCurrency(location.total_revenue)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      )
    }

    switch (activeTab) {
      case 'monthly':
        return renderMonthlyReport()
      case 'profitability':
        return renderProfitabilityReport()
      case 'comparison':
        return renderTruckComparison()
      case 'materials':
        return renderMaterialAnalysis()
      case 'locations':
        return renderLocationAnalysis()
      default:
        return renderMonthlyReport()
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">التقارير والتحليلات</h1>
        <button className="btn-outline flex items-center gap-2">
          <Download className="h-4 w-4" />
          تصدير PDF
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <select
              value={filters.truck_id || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, truck_id: e.target.value ? Number(e.target.value) : undefined }))}
              className="input"
            >
              <option value="">جميع الشاحنات</option>
              {trucks.map(truck => (
                <option key={truck.id} value={truck.id}>
                  {truck.plate_number} - {truck.driver_name || 'بدون سائق'}
                </option>
              ))}
            </select>

            <select
              value={filters.year || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, year: e.target.value ? Number(e.target.value) : undefined }))}
              className="input"
            >
              <option value="">اختر السنة</option>
              {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>

            <select
              value={filters.month || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, month: e.target.value ? Number(e.target.value) : undefined }))}
              className="input"
            >
              <option value="">اختر الشهر</option>
              {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                <option key={month} value={month}>
                  {new Date(2023, month - 1).toLocaleDateString('ar-SA', { month: 'long' })}
                </option>
              ))}
            </select>

            <input
              type="date"
              value={filters.start_date || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
              className="input"
              placeholder="من تاريخ"
            />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="card">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  )
}

export default ReportsPage
