@echo off
title Trucks Management System - Direct Launch
color 0B
echo ========================================
echo    Trucks Management System
echo    Direct Launch Method
echo ========================================
echo.

cd /d "%~dp0"

echo Installing Electron locally (one time only)...
echo This ensures the app starts properly.
echo.

REM Install electron locally to avoid npx issues
call npm install electron --no-save

echo.
echo Starting application...
echo.

REM Try multiple methods to start the app
if exist "node_modules\.bin\electron.cmd" (
    echo Method 1: Using local electron...
    start "" "node_modules\.bin\electron.cmd" resources\app
    echo Application should start now!
) else if exist "node_modules\electron\dist\electron.exe" (
    echo Method 2: Using electron.exe directly...
    start "" "node_modules\electron\dist\electron.exe" resources\app
    echo Application should start now!
) else (
    echo Method 3: Using npx...
    start "" cmd /c "npx electron resources\app"
    echo Application should start now!
)

echo.
echo ========================================
echo Application Status:
echo - If a new window opened: SUCCESS!
echo - If no window: Try running as administrator
echo - Check Windows taskbar for the app
echo ========================================
echo.
echo Press any key to close this window...
pause >nul
