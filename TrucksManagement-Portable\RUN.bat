@echo off
title Trucks Management System
echo ========================================
echo    Trucks Management System
echo ========================================
echo.

cd /d "%~dp0"

echo Starting application...
echo.

REM Try to run with npx electron and keep window open
echo Attempting to start with Electron...
echo Please wait, this may take a few minutes on first run...
echo.

start /wait cmd /c "npx electron resources\app"

REM Check if electron is now available locally
if exist "node_modules\.bin\electron.cmd" (
    echo.
    echo Electron installed successfully! Starting application...
    start "" "node_modules\.bin\electron.cmd" resources\app
) else (
    echo.
    echo Trying alternative startup method...
    start "" npx electron resources\app
)

echo.
echo Application should start in a new window...
echo If no window appears, check for error messages above.
echo.
pause
