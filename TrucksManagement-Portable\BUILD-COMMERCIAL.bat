@echo off
chcp 65001 >nul
title Building Trucks Management Professional

echo.
echo ==========================================
echo   Building Trucks Management Professional
echo ==========================================
echo.

echo Checking system...

REM التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تثبيت Node.js أولاً من:
    echo    https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متاح

REM التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود!
    pause
    exit /b 1
)

echo ✅ ملف package.json موجود

echo.
echo 📦 جاري تثبيت المتطلبات...
npm install

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات

echo.
echo 🔧 جاري إعادة بناء المكتبات الأصلية...
npm run rebuild

if errorlevel 1 (
    echo ⚠️ تحذير: فشل في إعادة بناء بعض المكتبات
    echo 🔄 المتابعة مع البناء...
)

echo.
echo 📁 جاري إنشاء مجلد البناء...
if not exist "dist" mkdir dist

echo.
echo 🏗️ جاري بناء التطبيق للمنصات المختلفة...
echo.

echo 🪟 بناء إصدار Windows...
npm run build-win

if errorlevel 1 (
    echo ❌ فشل في بناء إصدار Windows
    echo 🔄 جاري المحاولة مع إعدادات مختلفة...
    
    npx electron-builder --win --x64
    
    if errorlevel 1 (
        echo ❌ فشل في البناء النهائي لـ Windows
        echo.
        echo 💡 تأكد من:
        echo    1. تثبيت Visual Studio Build Tools
        echo    2. تثبيت Python 3.x
        echo    3. وجود ملفات الأيقونات في مجلد build
        echo.
        pause
        exit /b 1
    )
)

echo ✅ تم بناء إصدار Windows بنجاح

echo.
echo 📊 عرض نتائج البناء...
echo.

if exist "dist" (
    echo 📁 ملفات البناء في مجلد dist:
    dir dist /b
    echo.
    
    echo 📏 أحجام الملفات:
    for %%f in (dist\*.*) do (
        echo    %%~nxf - %%~zf bytes
    )
    echo.
)

echo.
echo 🎉 اكتمل بناء نظام إدارة الشاحنات المحترف!
echo.
echo 📦 الملفات الجاهزة للتوزيع:
echo    📁 مجلد: dist\
echo    🪟 Windows: TrucksManagement-Professional-Setup-2.0.0.exe
echo    📦 Portable: TrucksManagement-Professional-Portable-2.0.0.exe
echo.

echo 🚀 يمكنك الآن:
echo    1. تثبيت التطبيق من ملف Setup
echo    2. تشغيل النسخة المحمولة مباشرة
echo    3. توزيع الملفات للعملاء
echo.

echo 📋 ملاحظات مهمة:
echo    - تأكد من اختبار التطبيق قبل التوزيع
echo    - احتفظ بنسخة من ملفات المصدر
echo    - راجع ملف LICENSE للشروط التجارية
echo.

echo 💼 للاستخدام التجاري:
echo    - الترخيص التجاري مُفعل
echo    - يمكن التوزيع للعملاء
echo    - دعم فني متاح
echo.

pause
