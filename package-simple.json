{"name": "trucks-management-system", "version": "1.0.0", "description": "نظام إدارة شاحنات النقل الثقيل", "main": "dist/main.js", "scripts": {"start": "electron dist/main.js", "dist": "electron-builder", "pack": "electron-builder --dir"}, "keywords": ["electron", "trucks", "management"], "author": "Trucks Management System", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.trucksmanagement.app", "productName": "نظام إدارة الشاحنات", "directories": {"output": "release"}, "files": ["dist/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة الشاحنات"}}}