# دليل التثبيت والتشغيل - نظام إدارة الشاحنات

## 🚀 طرق التشغيل السريع

### الطريقة الأولى: استخدام ملفات التشغيل الجاهزة

#### للتطوير:
1. انقر نقراً مزدوجاً على ملف `dev.bat`
2. انتظر حتى يتم تثبيت التبعيات وتشغيل الخادم
3. سيفتح التطبيق تلقائياً

#### للإنتاج:
1. انقر نقراً مزدوجاً على ملف `start.bat`
2. انتظر حتى يتم البناء والتشغيل

### الطريقة الثانية: استخدام سطر الأوامر

#### 1. تثبيت Node.js
تأكد من تثبيت Node.js الإصدار 18 أو أحدث من:
https://nodejs.org/

#### 2. فتح سطر الأوامر
- اضغط `Win + R`
- اكتب `cmd` واضغط Enter
- انتقل إلى مجلد المشروع:
```cmd
cd C:\TrucksProject
```

#### 3. تثبيت التبعيات
```cmd
npm install
```

#### 4. تشغيل المشروع

**للتطوير:**
```cmd
npm run dev
```

**للإنتاج:**
```cmd
npm run build
npm run start
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "npm is not recognized"
**الحل:**
1. تأكد من تثبيت Node.js بشكل صحيح
2. أعد تشغيل الكمبيوتر
3. تحقق من متغيرات البيئة (Environment Variables)

### مشكلة: "Permission denied"
**الحل:**
1. شغل سطر الأوامر كمدير (Run as Administrator)
2. أو استخدم:
```cmd
npm install --no-optional
```

### مشكلة: "Module not found"
**الحل:**
1. احذف مجلد `node_modules`
2. احذف ملف `package-lock.json`
3. شغل `npm install` مرة أخرى

### مشكلة: "Port already in use"
**الحل:**
1. أغلق أي تطبيقات أخرى تستخدم المنفذ 3000
2. أو غير المنفذ في ملف `vite.config.ts`

## 📁 هيكل المشروع

```
TrucksProject/
├── electron/           # ملفات Electron الرئيسية
│   ├── main.ts         # العملية الرئيسية
│   ├── preload.ts      # سكريبت التحميل المسبق
│   └── database/       # إدارة قاعدة البيانات
├── src/                # ملفات React
│   ├── components/     # المكونات المشتركة
│   ├── pages/          # صفحات التطبيق
│   └── types/          # تعريفات TypeScript
├── public/             # الملفات العامة
├── dist/               # ملفات البناء (تُنشأ تلقائياً)
└── release/            # ملفات التوزيع (تُنشأ تلقائياً)
```

## 🗃️ قاعدة البيانات

- **النوع**: SQLite
- **الموقع**: `%USERPROFILE%\AppData\Roaming\trucks-management-system\trucks_management.db`
- **النسخ الاحتياطي**: انسخ الملف أعلاه لحفظ البيانات

## 🔄 التحديثات

لتحديث المشروع:
1. احصل على أحدث إصدار من الكود
2. شغل `npm install` لتحديث التبعيات
3. شغل `npm run build` لإعادة البناء

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من هذا الدليل أولاً
2. تأكد من تثبيت Node.js بشكل صحيح
3. تحقق من اتصال الإنترنت أثناء التثبيت
4. جرب إعادة تشغيل الكمبيوتر

## ✅ التحقق من نجاح التثبيت

عند نجاح التشغيل، ستظهر:
1. نافذة التطبيق مع واجهة نظام إدارة الشاحنات
2. قائمة جانبية تحتوي على: الرئيسية، الشاحنات، الرحلات، المصروفات، التقارير
3. بيانات تجريبية (3 شاحنات كمثال)

---

**نصيحة**: احتفظ بنسخة احتياطية من ملف قاعدة البيانات بانتظام! 💾
