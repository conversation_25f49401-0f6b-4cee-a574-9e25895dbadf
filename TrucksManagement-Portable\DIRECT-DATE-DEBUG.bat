@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔍 تشخيص مباشر لمشكلة التاريخ
echo ========================================
echo.
echo 🎯 الهدف: معرفة السبب الحقيقي لتغيير التاريخ
echo.
echo ========================================
echo    🚀 خطوات التشخيص المباشر
echo ========================================
echo.
echo 1️⃣ شغل التطبيق:
echo    └── استخدم DIRECT-RUN.bat
echo.
echo 2️⃣ افتح Developer Tools فوراً:
echo    ├── اضغط F12
echo    ├── اذهب لتبويب Console
echo    └── امسح الرسائل
echo.
echo 3️⃣ اختبر حقل التاريخ مباشرة:
echo    ├── اذهب لصفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── في Console اكتب:
echo    │   document.getElementById('trip-date').value = '2025-06-10'
echo    ├── اضغط Enter
echo    ├── تحقق من الحقل - يجب أن يظهر 2025-06-10
echo    └── لا تملأ أي حقول أخرى بعد
echo.
echo 4️⃣ اختبر قراءة التاريخ:
echo    ├── في Console اكتب:
echo    │   document.getElementById('trip-date').value
echo    ├── اضغط Enter
echo    ├── يجب أن ترى: "2025-06-10"
echo    ├── إذا رأيت شيء آخر، فهناك مشكلة في الحقل
echo    └── انسخ النتيجة
echo.
echo 5️⃣ اختبر دالة addTrip مباشرة:
echo    ├── املأ جميع الحقول المطلوبة:
echo    │   ├── الشاحنة: أي شاحنة
echo    │   ├── المادة: رمل
echo    │   ├── الكمية: 10
echo    │   ├── السعر: 100
echo    │   ├── موقع التحميل: اختبار
echo    │   └── موقع التفريغ: اختبار
echo    ├── تأكد أن التاريخ ما زال 2025-06-10
echo    ├── اضغط "تسجيل الرحلة"
echo    └── راقب Console بعناية
echo.
echo 6️⃣ ابحث عن هذه الرسائل بالترتيب:
echo    ├── "🔍 فحص حقل التاريخ: {value: '2025-06-10'}"
echo    ├── "📅 التاريخ النهائي المستخدم: 2025-06-10"
echo    ├── "🆕 الرحلة الجديدة: {date: '2025-06-10'}"
echo    ├── "📊 المصفوفة بعد الإضافة: X رحلة"
echo    ├── "💾 تم حفظ البيانات في localStorage"
echo    └── "📅 عرض رحلة: X تاريخ: 2025-06-10"
echo.
echo 7️⃣ فحص localStorage مباشرة:
echo    ├── اضغط F12 → Application → Local Storage
echo    ├── ابحث عن مفتاح "trips"
echo    ├── انقر عليه لرؤية المحتوى
echo    ├── ابحث عن آخر رحلة في القائمة
echo    ├── تحقق من حقل "date" في آخر رحلة
echo    └── يجب أن يكون "2025-06-10"
echo.
echo 8️⃣ فحص المصفوفة في الذاكرة:
echo    ├── في Console اكتب: trips
echo    ├── اضغط Enter
echo    ├── ابحث عن آخر رحلة في المصفوفة
echo    ├── تحقق من حقل date
echo    ├── اكتب: trips[trips.length-1].date
echo    └── يجب أن ترى "2025-06-10"
echo.
echo ========================================
echo    🔍 تحليل النتائج
echo ========================================
echo.
echo حسب نتائج الفحص:
echo.
echo ✅ إذا كان التاريخ صحيح في جميع المراحل:
echo    ├── المشكلة في displayTrips أو في العرض
echo    ├── تحقق من دالة displayTrips
echo    └── قد تكون مشكلة في تنسيق التاريخ
echo.
echo ❌ إذا تغير التاريخ في مرحلة "فحص حقل التاريخ":
echo    ├── المشكلة في قراءة الحقل
echo    ├── قد يكون هناك JavaScript يغير الحقل
echo    └── تحقق من أي event listeners على الحقل
echo.
echo ❌ إذا تغير التاريخ في مرحلة "الرحلة الجديدة":
echo    ├── المشكلة في إنشاء الكائن
echo    ├── تحقق من دالة addTrip
echo    └── قد تكون مشكلة في المتغير date
echo.
echo ❌ إذا تغير التاريخ في localStorage:
echo    ├── المشكلة في الحفظ
echo    ├── تحقق من localStorage.setItem
echo    └── قد تكون مشكلة في JSON.stringify
echo.
echo ❌ إذا تغير التاريخ في العرض:
echo    ├── المشكلة في displayTrips
echo    ├── تحقق من كيفية عرض التاريخ
echo    └── قد تكون مشكلة في HTML template
echo.
echo ========================================
echo    🛠️ حلول حسب المرحلة
echo ========================================
echo.
echo 🔧 إذا كانت المشكلة في قراءة الحقل:
echo    ├── تحقق من وجود حقل trip-date
echo    ├── تأكد من عدم وجود حقول متعددة بنفس الـ id
echo    ├── جرب querySelector بدلاً من getElementById
echo    └── تحقق من أي تداخل في النماذج
echo.
echo 🔧 إذا كانت المشكلة في إنشاء الكائن:
echo    ├── تحقق من المتغير date قبل إنشاء newTrip
echo    ├── تأكد من عدم تعديل date في مكان آخر
echo    ├── جرب استخدام const بدلاً من let
echo    └── تحقق من أي دوال تؤثر على date
echo.
echo 🔧 إذا كانت المشكلة في الحفظ:
echo    ├── تحقق من JSON.stringify(trips)
echo    ├── تأكد من أن المصفوفة تحتوي على البيانات الصحيحة
echo    ├── جرب حفظ الرحلة منفردة
echo    └── تحقق من أي تداخل في الحفظ
echo.
echo 🔧 إذا كانت المشكلة في العرض:
echo    ├── تحقق من دالة displayTrips
echo    ├── تأكد من أن trip.date يُعرض بشكل صحيح
echo    ├── جرب console.log في displayTrips
echo    └── تحقق من HTML template للرحلة
echo.
echo ========================================
echo    📝 تقرير التشخيص
echo ========================================
echo.
echo بعد إجراء الفحوصات، اكتب تقرير:
echo.
echo 1. التاريخ في الحقل: ___________
echo 2. التاريخ في "فحص حقل التاريخ": ___________
echo 3. التاريخ في "الرحلة الجديدة": ___________
echo 4. التاريخ في localStorage: ___________
echo 5. التاريخ في المصفوفة: ___________
echo 6. التاريخ المعروض في القائمة: ___________
echo.
echo هذا التقرير سيحدد بالضبط في أي مرحلة يتغير التاريخ.
echo.
pause
