@echo off
echo ========================================
echo    نظام إدارة الشاحنات - التشغيل التدريجي
echo ========================================
echo.

echo الخطوة 1: التحقق من Node.js...
node --version
if errorlevel 1 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo الخطوة 2: التحقق من npm...
npm --version
if errorlevel 1 (
    echo خطأ: npm غير متوفر!
    pause
    exit /b 1
)

echo الخطوة 3: الانتقال لمجلد المشروع...
cd /d "C:\TrucksProject"
if errorlevel 1 (
    echo خطأ: لا يمكن العثور على مجلد المشروع!
    pause
    exit /b 1
)

echo الخطوة 4: تثبيت التبعيات...
echo هذا قد يستغرق بضع دقائق...
npm install --verbose
if errorlevel 1 (
    echo خطأ في تثبيت التبعيات!
    echo جرب تشغيل الأمر كمدير
    pause
    exit /b 1
)

echo الخطوة 5: بناء المشروع...
npm run build:main
if errorlevel 1 (
    echo خطأ في بناء المشروع!
    pause
    exit /b 1
)

echo الخطوة 6: تشغيل التطبيق...
echo سيفتح التطبيق الآن...
npm run dev

echo إذا لم يفتح التطبيق، اضغط أي مفتاح للمحاولة مرة أخرى...
pause
