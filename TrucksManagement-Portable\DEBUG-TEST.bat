@echo off
echo.
echo ========================================
echo 🔍 اختبار التشخيص - إصلاح نهائي
echo ========================================
echo.
echo 🛠️ الإصلاح الجديد:
echo    ✅ إضافة try-catch شامل
echo    ✅ إغلاق النافذة حتى لو فشل الحفظ
echo    ✅ رسائل خطأ مفصلة
echo    ✅ تشخيص كامل للمشكلة
echo.
echo 📋 خطوات التشخيص:
echo.
echo 1️⃣  سيتم تشغيل التطبيق
echo 2️⃣  افتح Developer Tools (F12)
echo 3️⃣  راقب جميع الرسائل والأخطاء
echo 4️⃣  اختبر إضافة شاحنة
echo.
pause

echo 📱 تشغيل التطبيق...
start "" npx electron resources/app

echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 🔍 تعليمات التشخيص المفصلة:
echo.
echo 📊 افتح Developer Tools:
echo    - اضغط F12
echo    - انتقل لتبويب Console
echo    - راقب جميع الرسائل (عادية، تحذيرات، أخطاء)
echo.
echo 🚛 اختبار الشاحنات:
echo    1. انقر "إضافة شاحنة جديدة"
echo    2. املأ رقم اللوحة: تست 1234
echo    3. املأ اسم السائق: اختبار التشخيص
echo    4. انقر "إضافة"
echo    5. راقب الكونسول - يجب أن ترى:
echo       "🚛 بدء عملية إضافة شاحنة..."
echo       "📝 البيانات المدخلة: ..."
echo       "💾 تم حفظ الشاحنة في قاعدة البيانات" أو تحذير
echo       "🎯 الآن سيتم إغلاق النافذة..."
echo       "🔄 محاولة إغلاق النافذة..."
echo       "✅ تم إخفاء النافذة مباشرة"
echo.
echo 🎯 النتائج المتوقعة:
echo.
echo ✅ إذا رأيت "✅ تم إخفاء النافذة مباشرة":
echo    - النافذة يجب أن تختفي فوراً
echo    - إذا لم تختف = مشكلة في CSS أو DOM
echo.
echo ❌ إذا لم تر هذه الرسالة:
echo    - هناك خطأ JavaScript يوقف التنفيذ
echo    - ابحث عن رسائل خطأ حمراء في الكونسول
echo.
echo 🐛 رسائل الخطأ المحتملة:
echo    - "❌ خطأ في حفظ الشاحنة" = مشكلة في الحفظ
echo    - "❌ لم يتم العثور على النافذة" = مشكلة في HTML
echo    - "⚠️ فشل حفظ قاعدة البيانات" = مشكلة في SQLite
echo.
echo 💰 اختبار المصروفات:
echo    - نفس الخطوات مع "إضافة مصروف جديد"
echo    - راقب رسائل مشابهة في الكونسول
echo.
echo 📋 تقرير النتائج:
echo    بعد الاختبار، أخبرني:
echo    1. هل رأيت "✅ تم إخفاء النافذة مباشرة"؟
echo    2. هل اختفت النافذة فعلاً؟
echo    3. ما هي رسائل الخطأ (إن وجدت)؟
echo.
pause
