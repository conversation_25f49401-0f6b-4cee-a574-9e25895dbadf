@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔧 إجبار مسح Cache وإعادة التحميل
echo ========================================
echo.
echo 🎯 المشكلة: التطبيق يستخدم نسخة قديمة من الملف
echo 🔧 الحل: مسح شامل للـ cache وإعادة تحميل قسري
echo.
echo ========================================
echo    🚀 خطوات الإجبار الشامل
========================================
echo.
echo 1️⃣ أغلق التطبيق تماماً:
echo    ├── أغلق جميع نوافذ التطبيق
echo    ├── اضغط Ctrl+Shift+Esc لفتح Task Manager
echo    ├── ابحث عن عمليات "Electron" أو "TrucksManagement"
echo    ├── أنهِ جميع العمليات المتعلقة
echo    ├── تأكد من عدم وجود أي عملية متبقية
echo    └── انتظر 15 ثانية
echo.
echo 2️⃣ مسح ملفات Cache:
echo    ├── اذهب لمجلد: %%APPDATA%%\trucks-management-system
echo    ├── احذف مجلد "Cache" إذا وجد
echo    ├── احذف مجلد "GPUCache" إذا وجد
echo    ├── احذف مجلد "Session Storage" إذا وجد
echo    └── احذف ملف "Preferences" إذا وجد
echo.
echo 3️⃣ نسخ الملف المُحدث:

copy "resources\app\dist\renderer\trucks-app.html" "resources\app\trucks-app-updated.html" >nul
copy "resources\app\trucks-app-updated.html" "resources\app\dist\renderer\trucks-app.html" >nul

echo ✅ تم نسخ الملف المُحدث

echo.
echo 4️⃣ شغل التطبيق مع مسح Cache:
echo    └── استخدم DIRECT-RUN.bat
echo.
echo 5️⃣ مسح Cache المتصفح فوراً:
echo    ├── اضغط F12 لفتح Developer Tools
echo    ├── اضغط F12 مرة أخرى لإغلاقه
echo    ├── اضغط Ctrl+Shift+R للتحديث القسري
echo    ├── اضغط Ctrl+F5 للتحديث الشامل
echo    ├── اضغط F12 مرة أخرى لفتح Developer Tools
echo    ├── اذهب لتبويب Application
echo    ├── في القائمة اليسرى اختر Storage
echo    ├── اضغط "Clear storage"
echo    ├── تأكد من تحديد جميع الخيارات
echo    ├── اضغط "Clear site data"
echo    └── أعد تحميل الصفحة (F5)
echo.
echo 6️⃣ اختبار فوري:
echo    ├── اذهب لصفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── تحقق من وجود حقل التاريخ
echo    ├── غير التاريخ لـ 2025-06-23
echo    ├── أغلق النموذج
echo    ├── اعد فتحه
echo    ├── تحقق: هل التاريخ ما زال 2025-06-23؟
echo    └── إذا كان نعم، فالمشكلة حُلت!
echo.
echo ========================================
echo    🔍 تشخيص متقدم
echo ========================================
echo.
echo إذا لم تعمل الخطوات السابقة:
echo.
echo 🔧 فحص الملف المُحمل:
echo    ├── افتح Developer Tools (F12)
echo    ├── اذهب لتبويب Sources
echo    ├── ابحث عن trucks-app.html
echo    ├── افتح الملف وابحث عن السطر 5124
echo    ├── يجب أن ترى: "if (!tripDateField.value)"
echo    └── إذا لم تره، فالملف القديم ما زال يُحمل
echo.
echo 🔧 فحص دالة showAddTripModal:
echo    ├── في Console اكتب: showAddTripModal.toString()
echo    ├── اضغط Enter
echo    ├── تحقق من وجود "if (!tripDateField.value)"
echo    └── إذا لم تجده، فالدالة القديمة ما زالت محملة
echo.
echo 🔧 فحص حقل التاريخ:
echo    ├── افتح نموذج الرحلة
echo    ├── في Console اكتب: document.getElementById('trip-date')
echo    ├── تحقق من وجود الحقل
echo    ├── اكتب: document.getElementById('trip-date').value
echo    └── تحقق من القيمة الحالية
echo.
echo ========================================
echo    💡 حلول إضافية
echo ========================================
echo.
echo إذا استمرت المشكلة:
echo.
echo 🔄 إعادة تشغيل النظام:
echo    ├── احفظ عملك
echo    ├── أعد تشغيل الكمبيوتر
echo    ├── شغل التطبيق من جديد
echo    └── اختبر المشكلة
echo.
echo 🔧 تحقق من main.js:
echo    ├── تأكد من أن main.js يشير للملف الصحيح
echo    ├── resources/app/dist/renderer/trucks-app.html
echo    └── وليس ملف آخر
echo.
echo 🛠️ إعادة بناء التطبيق:
echo    ├── استخدم BUILD-COMMERCIAL.bat
echo    ├── أو انسخ الملف يدوياً
echo    └── تأكد من التحديث
echo.
pause
