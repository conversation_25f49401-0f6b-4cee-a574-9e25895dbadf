# 🗄️ دليل قاعدة البيانات SQLite

## 📊 **إجابة سؤالك: أين يتم حفظ البيانات؟**

### **✅ الآن البيانات محفوظة في:**

#### **🗄️ قاعدة بيانات SQLite حقيقية:**
- **الموقع**: `TrucksManagement-Portable/data/trucks_database.db`
- **النوع**: ملف قاعدة بيانات SQLite دائم
- **الحجم**: ينمو تلقائياً مع البيانات
- **الأمان**: محفوظة على القرص الصلب

#### **💾 النسخ الاحتياطية:**
- **تلقائية**: كل 30 دقيقة
- **الموقع**: `TrucksManagement-Portable/data/backup_*.db`
- **التنسيق**: `backup_2024-01-15T10-30-00.db`

---

## 🏗️ **هيكل قاعدة البيانات**

### **📋 الجداول المتاحة:**

#### **1️⃣ جدول الشاحنات (trucks):**
```sql
CREATE TABLE trucks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plate TEXT UNIQUE NOT NULL,           -- رقم اللوحة
    driver TEXT NOT NULL,                 -- اسم السائق
    status TEXT DEFAULT 'active',         -- الحالة
    notes TEXT,                          -- ملاحظات
    model TEXT,                          -- الموديل
    year INTEGER,                        -- سنة الصنع
    insurance_data TEXT,                 -- بيانات التأمين (JSON)
    license_data TEXT,                   -- بيانات الرخصة (JSON)
    maintenance_data TEXT,               -- بيانات الصيانة (JSON)
    fuel_data TEXT,                      -- بيانات الوقود (JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **2️⃣ جدول الرحلات (trips):**
```sql
CREATE TABLE trips (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    truck TEXT NOT NULL,                 -- رقم الشاحنة
    material TEXT NOT NULL,              -- نوع المادة
    quantity REAL NOT NULL,              -- الكمية
    price REAL NOT NULL,                 -- السعر
    loading TEXT NOT NULL,               -- موقع التحميل
    unloading TEXT NOT NULL,             -- موقع التفريغ
    total REAL NOT NULL,                 -- الإجمالي
    date DATE NOT NULL,                  -- التاريخ
    notes TEXT,                          -- ملاحظات
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (truck) REFERENCES trucks(plate)
);
```

#### **3️⃣ جدول المصروفات (expenses):**
```sql
CREATE TABLE expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    truck TEXT NOT NULL,                 -- رقم الشاحنة
    type TEXT NOT NULL,                  -- نوع المصروف
    amount REAL NOT NULL,                -- المبلغ
    description TEXT NOT NULL,           -- الوصف
    date DATE NOT NULL,                  -- التاريخ
    receipt_number TEXT,                 -- رقم الإيصال
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (truck) REFERENCES trucks(plate)
);
```

#### **4️⃣ جدول الرواتب (salaries):**
```sql
CREATE TABLE salaries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    driver_name TEXT NOT NULL,           -- اسم السائق
    truck TEXT NOT NULL,                 -- رقم الشاحنة
    basic_salary REAL NOT NULL,          -- الراتب الأساسي
    allowances REAL DEFAULT 0,           -- البدلات
    bonuses REAL DEFAULT 0,              -- المكافآت
    deductions REAL DEFAULT 0,           -- الخصومات
    net_salary REAL NOT NULL,            -- صافي الراتب
    payment_date DATE,                   -- تاريخ الدفع
    status TEXT DEFAULT 'pending',       -- الحالة
    month TEXT NOT NULL,                 -- الشهر
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **5️⃣ جدول الميزانيات (budgets):**
```sql
CREATE TABLE budgets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category TEXT NOT NULL,              -- الفئة
    monthly_budget REAL NOT NULL,        -- الميزانية الشهرية
    current_spent REAL DEFAULT 0,        -- المصروف الحالي
    remaining REAL NOT NULL,             -- المتبقي
    month TEXT NOT NULL,                 -- الشهر
    alert_threshold INTEGER DEFAULT 80,  -- حد التنبيه
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **6️⃣ جدول الإعدادات (settings):**
```sql
CREATE TABLE settings (
    key TEXT PRIMARY KEY,                -- المفتاح
    value TEXT NOT NULL,                 -- القيمة
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔄 **كيف يتم الحفظ؟**

### **📝 عند إضافة شاحنة جديدة:**
1. **إضافة للذاكرة** - للعرض السريع
2. **حفظ في SQLite** - للحفظ الدائم
3. **تحديث العرض** - إظهار البيانات الجديدة
4. **رسالة تأكيد** - "تم حفظ الشاحنة في قاعدة البيانات!"

### **🛣️ عند تسجيل رحلة:**
1. **إضافة للذاكرة** - للعرض السريع
2. **حفظ في SQLite** - للحفظ الدائم
3. **تحديث الإحصائيات** - حساب الأرباح
4. **رسالة تأكيد** - "تم حفظ الرحلة في قاعدة البيانات!"

### **💰 عند إضافة مصروف:**
1. **إضافة للذاكرة** - للعرض السريع
2. **حفظ في SQLite** - للحفظ الدائم
3. **تحديث الميزانية** - حساب المتبقي
4. **رسالة تأكيد** - "تم حفظ المصروف في قاعدة البيانات!"

---

## 🔒 **الأمان والموثوقية**

### **✅ مميزات قاعدة البيانات:**
- **حفظ دائم** - لا تختفي البيانات أبداً
- **سرعة عالية** - SQLite محسن للأداء
- **موثوقية** - مقاوم للأخطاء والانقطاع
- **نسخ احتياطية** - تلقائية كل 30 دقيقة
- **استرداد** - يمكن استرداد البيانات من النسخ

### **🛡️ الحماية من فقدان البيانات:**
- **ACID Compliance** - ضمان تكامل البيانات
- **Transaction Safety** - حفظ آمن للمعاملات
- **Crash Recovery** - استرداد تلقائي بعد الأخطاء
- **Backup System** - نظام نسخ احتياطي متقدم

---

## 📁 **مواقع الملفات**

### **🗄️ ملفات قاعدة البيانات:**
```
TrucksManagement-Portable/
├── data/
│   ├── trucks_database.db          # قاعدة البيانات الرئيسية
│   ├── backup_2024-01-15T10-30.db  # نسخة احتياطية
│   ├── backup_2024-01-15T11-00.db  # نسخة احتياطية
│   └── backup_2024-01-15T11-30.db  # نسخة احتياطية
├── main.js                         # ملف التطبيق الرئيسي
├── package.json                    # إعدادات المشروع
└── resources/                      # ملفات الواجهة
```

### **💾 حجم البيانات المتوقع:**
- **قاعدة بيانات فارغة**: ~50 KB
- **100 شاحنة**: ~200 KB
- **1000 رحلة**: ~500 KB
- **1000 مصروف**: ~300 KB
- **المجموع لسنة كاملة**: ~2-5 MB

---

## 🔧 **إدارة قاعدة البيانات**

### **📊 عرض الإحصائيات:**
```javascript
// في وحدة التحكم
const stats = dataManager.getDatabaseStats();
console.log('إحصائيات قاعدة البيانات:', stats);
```

### **💾 إنشاء نسخة احتياطية يدوية:**
```javascript
// في وحدة التحكم
dataManager.createDatabaseBackup();
```

### **🔍 فحص قاعدة البيانات:**
- **الموقع**: `TrucksManagement-Portable/data/trucks_database.db`
- **الأدوات**: يمكن فتحها بـ DB Browser for SQLite
- **الحجم**: يظهر في خصائص الملف

---

## 🚀 **المميزات الجديدة**

### **✅ ما تم إضافته:**
- **قاعدة بيانات SQLite** حقيقية ودائمة
- **حفظ تلقائي** لجميع البيانات
- **نسخ احتياطية** تلقائية
- **استرداد البيانات** عند إعادة التشغيل
- **رسائل تأكيد** واضحة للحفظ
- **إحصائيات قاعدة البيانات** مفصلة

### **🎯 الفوائد:**
- **لا فقدان للبيانات** أبداً
- **أداء محسن** مع البيانات الكبيرة
- **موثوقية عالية** في الحفظ
- **سهولة النسخ الاحتياطي** والاسترداد
- **تتبع كامل** لجميع العمليات

---

## 🧪 **اختبار قاعدة البيانات**

### **✅ للتأكد من عمل قاعدة البيانات:**

1. **أضف شاحنة جديدة**
   - ستظهر رسالة: "تم حفظ الشاحنة في قاعدة البيانات!"
   - تحقق من وجود ملف: `data/trucks_database.db`

2. **أغلق التطبيق وأعد فتحه**
   - ستجد جميع البيانات محفوظة
   - لا فقدان لأي معلومة

3. **تحقق من النسخ الاحتياطية**
   - ستجد ملفات: `data/backup_*.db`
   - تُنشأ كل 30 دقيقة تلقائياً

### **🔍 علامات النجاح:**
- **✅ رسائل تأكيد** عند الحفظ
- **✅ ملف قاعدة البيانات** موجود
- **✅ البيانات محفوظة** بعد إعادة التشغيل
- **✅ النسخ الاحتياطية** تُنشأ تلقائياً

---

## 🎉 **النتيجة النهائية**

### **🗄️ قاعدة بيانات حقيقية ومتقدمة:**
- **SQLite** - أسرع وأكثر موثوقية
- **حفظ دائم** - على القرص الصلب
- **نسخ احتياطية** - حماية كاملة
- **استرداد تلقائي** - عند إعادة التشغيل
- **أداء محسن** - للبيانات الكبيرة

### **💪 لا تقلق بشأن البيانات:**
- **محفوظة بأمان** في قاعدة بيانات حقيقية
- **لا تختفي** عند إغلاق التطبيق
- **نسخ احتياطية** تلقائية
- **استرداد سهل** في حالة المشاكل

**🎊 الآن بياناتك في أمان تام مع قاعدة بيانات SQLite المتقدمة!** 🗄️✨💙
