========================================
    نظام إدارة الشاحنات - طريقة التشغيل
========================================

🚀 طرق التشغيل (جرب بالترتيب):

1. انقر نقراً مزدوجاً على: DIRECT-RUN.bat (الأفضل)
2. انقر نقراً مزدوجاً على: LAUNCH.bat
3. انقر نقراً مزدوجاً على: RUN.bat
4. انقر بالزر الأيمن على Start-TrucksManagement.ps1 واختر "Run with PowerShell"

📋 المتطلبات:
- Windows 10/11
- Node.js (سيتم تحميله تلقائياً إذا لم يكن موجود)

⚠️ إذا لم يعمل:
1. تأكد من الاتصال بالإنترنت
2. شغل Command Prompt كمدير
3. اكتب: npx electron resources\app

🎯 النتيجة المتوقعة:
- سيفتح نافذة التطبيق
- ستظهر واجهة نظام إدارة الشاحنات
- ستجد 3 شاحنات تجريبية جاهزة

========================================
    Trucks Management System - How to Run
========================================

🚀 Ways to run (choose one):

1. Double-click: RUN.bat
2. Double-click: Start-TrucksManagement.bat
3. Right-click Start-TrucksManagement.ps1 and select "Run with PowerShell"

📋 Requirements:
- Windows 10/11
- Node.js (will be downloaded automatically if not present)

⚠️ If it doesn't work:
1. Make sure you have internet connection
2. Run Command Prompt as administrator
3. Type: npx electron resources\app

🎯 Expected result:
- Application window will open
- Trucks management interface will appear
- You'll find 3 sample trucks ready for testing
