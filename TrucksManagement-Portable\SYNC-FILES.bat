@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔄 مزامنة ملفات التطبيق
echo ========================================
echo.
echo 🎯 نسخ التحديثات بين الملفات:
echo.
echo 📁 الملف الأساسي:
echo    └── resources\app\trucks-app.html
echo.
echo 📁 ملف التشغيل:
echo    └── resources\app\dist\renderer\trucks-app.html
echo.
echo 🔄 جاري النسخ...

copy "resources\app\trucks-app.html" "resources\app\dist\renderer\trucks-app.html" >nul

if %errorlevel% == 0 (
    echo ✅ تم نسخ الملف بنجاح!
) else (
    echo ❌ فشل في نسخ الملف!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    🚀 اختبار التحديثات
echo ========================================
echo.
echo 1️⃣ أغلق التطبيق تماماً (إذا كان مفتوحاً)
echo.
echo 2️⃣ شغل التطبيق من جديد:
echo    └── استخدم DIRECT-RUN.bat
echo.
echo 3️⃣ امسح cache المتصفح:
echo    ├── اضغط Ctrl+Shift+I لفتح Developer Tools
echo    ├── اضغط Ctrl+Shift+R للتحديث القسري
echo    └── أو امسح Local Storage من تبويب Application
echo.
echo 4️⃣ اختبر حقول التاريخ:
echo    ├── افتح نموذج تسجيل رحلة جديدة
echo    ├── تحقق من ظهور حقل "تاريخ الرحلة"
echo    ├── افتح نموذج إضافة مصروف جديد
echo    └── تحقق من ظهور حقل "تاريخ المصروف"
echo.
echo ✅ يجب أن تظهر حقول التاريخ الآن!
echo.
pause
