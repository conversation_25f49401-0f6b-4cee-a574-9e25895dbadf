import React, { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, Search } from 'lucide-react'
import TruckForm from '../components/TruckForm'

interface Truck {
  id?: number
  plate_number: string
  driver_name?: string
  status: 'active' | 'maintenance' | 'inactive'
  notes?: string
  created_at?: string
  updated_at?: string
}

const TrucksPage: React.FC = () => {
  const [trucks, setTrucks] = useState<Truck[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingTruck, setEditingTruck] = useState<Truck | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadTrucks()
  }, [])

  const loadTrucks = async () => {
    try {
      setLoading(true)
      const data = await window.electronAPI.getTrucks()
      setTrucks(data)
    } catch (error) {
      console.error('Error loading trucks:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddTruck = () => {
    setEditingTruck(null)
    setShowForm(true)
  }

  const handleEditTruck = (truck: Truck) => {
    setEditingTruck(truck)
    setShowForm(true)
  }

  const handleDeleteTruck = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الشاحنة؟')) {
      try {
        await window.electronAPI.deleteTruck(id)
        await loadTrucks()
      } catch (error) {
        console.error('Error deleting truck:', error)
        alert('حدث خطأ أثناء حذف الشاحنة')
      }
    }
  }

  const handleFormSubmit = async (truckData: Omit<Truck, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      if (editingTruck) {
        await window.electronAPI.updateTruck(editingTruck.id!, truckData)
      } else {
        await window.electronAPI.addTruck(truckData)
      }
      await loadTrucks()
      setShowForm(false)
      setEditingTruck(null)
    } catch (error) {
      console.error('Error saving truck:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: 'نشطة', className: 'bg-green-100 text-green-800' },
      maintenance: { label: 'صيانة', className: 'bg-yellow-100 text-yellow-800' },
      inactive: { label: 'غير نشطة', className: 'bg-red-100 text-red-800' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const filteredTrucks = trucks.filter(truck =>
    truck.plate_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    truck.driver_name?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">إدارة الشاحنات</h1>
        <button
          onClick={handleAddTruck}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          إضافة شاحنة جديدة
        </button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="البحث برقم اللوحة أو اسم السائق..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="input pr-10"
        />
      </div>

      {/* Trucks Table */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  رقم اللوحة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  اسم السائق
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ملاحظات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTrucks.map((truck) => (
                <tr key={truck.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {truck.plate_number}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {truck.driver_name || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(truck.status)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {truck.notes || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEditTruck(truck)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteTruck(truck.id!)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {filteredTrucks.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد شاحنات مسجلة</p>
            </div>
          )}
        </div>
      </div>

      {/* Truck Form Modal */}
      {showForm && (
        <TruckForm
          truck={editingTruck}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false)
            setEditingTruck(null)
          }}
        />
      )}
    </div>
  )
}

export default TrucksPage
