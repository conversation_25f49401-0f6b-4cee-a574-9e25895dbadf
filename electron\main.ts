import { app, BrowserWindow, ipcMain } from 'electron'
import * as path from 'path'
import { SimpleDatabaseManager } from './database/SimpleDatabaseManager'

const isDev = process.env.NODE_ENV === 'development'

let mainWindow: BrowserWindow
let dbManager: SimpleDatabaseManager

function createWindow(): void {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
    },
    icon: path.join(__dirname, '../assets/truck-icon.png'),
    titleBarStyle: 'default',
    show: false,
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'))
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.on('closed', () => {
    mainWindow = null as any
  })
}

app.whenReady().then(async () => {
  // Initialize database
  dbManager = new SimpleDatabaseManager()
  try {
    await dbManager.initialize()
    console.log('Simple Database initialized successfully')
  } catch (error) {
    console.error('Database initialization failed:', error)
  }

  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// IPC handlers for database operations
ipcMain.handle('db:getTrucks', async () => {
  return dbManager.getTrucks()
})

ipcMain.handle('db:addTruck', async (_, truck) => {
  return dbManager.addTruck(truck)
})

ipcMain.handle('db:updateTruck', async (_, id, truck) => {
  return dbManager.updateTruck(id, truck)
})

ipcMain.handle('db:deleteTruck', async (_, id) => {
  return dbManager.deleteTruck(id)
})

ipcMain.handle('db:getTrips', async (_, filters) => {
  return dbManager.getTrips(filters)
})

ipcMain.handle('db:addTrip', async (_, trip) => {
  return dbManager.addTrip(trip)
})

ipcMain.handle('db:updateTrip', async (_, id, trip) => {
  return dbManager.updateTrip(id, trip)
})

ipcMain.handle('db:deleteTrip', async (_, id) => {
  return dbManager.deleteTrip(id)
})

ipcMain.handle('db:getExpenses', async (_, filters) => {
  return dbManager.getExpenses(filters)
})

ipcMain.handle('db:addExpense', async (_, expense) => {
  return dbManager.addExpense(expense)
})

ipcMain.handle('db:updateExpense', async (_, id, expense) => {
  return dbManager.updateExpense(id, expense)
})

ipcMain.handle('db:deleteExpense', async (_, id) => {
  return dbManager.deleteExpense(id)
})

ipcMain.handle('db:getReports', async (_, type, filters) => {
  return dbManager.getReports(type, filters)
})
