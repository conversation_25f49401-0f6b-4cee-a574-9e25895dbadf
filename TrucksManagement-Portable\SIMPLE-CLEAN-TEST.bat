@echo off
echo.
echo ========================================
echo    SIMPLE CLEAN TEST
echo ========================================
echo.
echo I completely simplified the addTrip function!
echo Removed all the complex tracking and debugging.
echo Now it's just basic: read data, create trip, save, display.
echo.
echo TEST STEPS:
echo 1. Close app completely
echo 2. Restart app
echo 3. Open F12 Developer Tools
echo 4. Go to Trips page
echo 5. Click "Add New Trip"
echo 6. Enter date: 2025-06-10
echo 7. Fill other fields:
echo    - Truck: any truck
echo    - Material: 3/4
echo    - Quantity: 22
echo    - Price: 120
echo    - Loading: 1
echo    - Unloading: 2
echo 8. Click Save
echo.
echo EXPECTED CONSOLE MESSAGES:
echo - "إضافة رحلة جديدة..."
echo - "البيانات: {truck: '...', date: '2025-06-10', ...}"
echo - "الرحلة الجديدة: {date: '2025-06-10', ...}"
echo.
echo EXPECTED RESULT:
echo - Trip should be saved with date: 2025-06-10
echo - Trip should appear in the list with correct date
echo - Alert: "تم حفظ الرحلة بنجاح!"
echo.
echo If the date is still wrong, the problem is deeper
echo in the browser or HTML structure.
echo.
echo Copy the console messages and tell me the result!
echo.
pause
