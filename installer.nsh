; Custom NSIS installer script for Trucks Management System

; Arabic language support
!define MUI_LANGDLL_ALLLANGUAGES

; Custom installer text
LangString INSTALLER_TITLE ${LANG_ARABIC} "نظام إدارة الشاحنات"
LangString INSTALLER_SUBTITLE ${LANG_ARABIC} "نظام شامل لإدارة شاحنات النقل الثقيل"

; Installation directory
!define INSTALL_DIR "$PROGRAMFILES64\TrucksManagement"

; Create desktop shortcut with Arabic name
!macro customInstall
  CreateShortCut "$DESKTOP\نظام إدارة الشاحنات.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  CreateShortCut "$SMPROGRAMS\نظام إدارة الشاحنات.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
!macroend

; Custom uninstaller
!macro customUnInstall
  Delete "$DESKTOP\نظام إدارة الشاحنات.lnk"
  Delete "$SMPROGRAMS\نظام إدارة الشاحنات.lnk"
!macroend
