@echo off
echo ========================================
echo اختبار رسالة التأكيد المخصصة
echo ========================================
echo.

echo تشغيل التطبيق...
start "" "TrucksManagement.exe"

echo.
echo ========================================
echo تعليمات الاختبار:
echo ========================================
echo.
echo 1. انتقل إلى صفحة المصروفات
echo 2. ابحث عن نوع مصروف غير مستخدم (مثل: "أخرى")
echo 3. اضغط على زر الحذف (🗑️) بجانب النوع
echo 4. تحقق من ظهور رسالة التأكيد باللغة العربية
echo 5. تأكد من وجود:
echo    - عنوان "تأكيد الحذف" باللون الأحمر
echo    - رسالة واضحة بالعربية
echo    - تفاصيل إضافية عن عدم إمكانية التراجع
echo    - زر "حذف" باللون الأحمر
echo    - زر "إلغاء" باللون الرمادي
echo 6. اختبر كلا الخيارين (حذف وإلغاء)
echo.
echo ========================================
echo النتائج المتوقعة:
echo ========================================
echo.
echo ✅ رسالة تأكيد باللغة العربية بدلاً من الإنجليزية
echo ✅ تصميم جميل ومتناسق مع النظام
echo ✅ أزرار واضحة ومفهومة
echo ✅ إغلاق النافذة عند النقر خارجها
echo ✅ تنفيذ الحذف عند الضغط على "حذف"
echo ✅ إلغاء العملية عند الضغط على "إلغاء"
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
