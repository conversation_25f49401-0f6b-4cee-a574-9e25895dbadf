@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔍 تشخيص متقدم للمشكلة
echo ========================================
echo.
echo 🎯 هدف التشخيص: معرفة السبب الحقيقي للمشكلة
echo.
echo ========================================
echo    📋 خطوات التشخيص
echo ========================================
echo.
echo 1️⃣ شغل التطبيق:
echo    └── استخدم DIRECT-RUN.bat
echo.
echo 2️⃣ افتح Developer Tools:
echo    ├── اضغط F12
echo    ├── اذهب لتبويب Console
echo    └── امسح الرسائل
echo.
echo 3️⃣ فحص الملف المُحمل:
echo    ├── اذهب لتبويب Sources
echo    ├── ابحث عن trucks-app.html
echo    ├── اضغط Ctrl+F للبحث
echo    ├── ابحث عن: "if (!tripDateField.value)"
echo    └── هل وجدته؟ إذا لم تجده، فالملف القديم يُحمل
echo.
echo 4️⃣ فحص دالة showAddTripModal:
echo    ├── في Console اكتب:
echo    │   showAddTripModal.toString()
echo    ├── اضغط Enter
echo    ├── ابحث في النتيجة عن: "if (!tripDateField.value)"
echo    └── هل وجدته؟ إذا لم تجده، فالدالة القديمة محملة
echo.
echo 5️⃣ فحص دالة updateStats:
echo    ├── في Console اكتب:
echo    │   updateStats.toString()
echo    ├── اضغط Enter
echo    ├── ابحث في النتيجة عن: "isHomePage"
echo    └── هل وجدته؟ إذا لم تجده، فالدالة القديمة محملة
echo.
echo 6️⃣ اختبار حقل التاريخ:
echo    ├── اذهب لصفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── في Console اكتب:
echo    │   document.getElementById('trip-date')
echo    ├── هل الحقل موجود؟
echo    ├── اكتب: document.getElementById('trip-date').value
echo    └── ما هي القيمة الحالية؟
echo.
echo 7️⃣ اختبار تغيير التاريخ:
echo    ├── في Console اكتب:
echo    │   document.getElementById('trip-date').value = '2025-06-23'
echo    ├── اضغط Enter
echo    ├── أغلق النموذج
echo    ├── اعد فتحه
echo    ├── في Console اكتب:
echo    │   document.getElementById('trip-date').value
echo    └── هل القيمة ما زالت 2025-06-23؟
echo.
echo ========================================
echo    📊 تحليل النتائج
echo ========================================
echo.
echo حسب نتائج الفحص:
echo.
echo ❌ إذا لم تجد "if (!tripDateField.value)" في Sources:
echo    └── المشكلة: الملف القديم ما زال يُحمل
echo    └── الحل: مسح cache أو إعادة تشغيل
echo.
echo ❌ إذا لم تجد "if (!tripDateField.value)" في showAddTripModal:
echo    └── المشكلة: الدالة القديمة ما زالت في الذاكرة
echo    └── الحل: إعادة تحميل الصفحة أو إعادة تشغيل
echo.
echo ❌ إذا لم تجد "isHomePage" في updateStats:
echo    └── المشكلة: دالة updateStats القديمة ما زالت تعمل
echo    └── الحل: إعادة تحميل الصفحة
echo.
echo ❌ إذا كان حقل التاريخ غير موجود:
echo    └── المشكلة: HTML القديم ما زال يُحمل
echo    └── الحل: مسح cache وإعادة تحميل
echo.
echo ❌ إذا كان التاريخ يُعاد تعيينه عند إعادة فتح النموذج:
echo    └── المشكلة: showAddTripModal القديمة ما زالت تعمل
echo    └── الحل: إعادة تحميل الصفحة
echo.
echo ========================================
echo    🛠️ حلول حسب النتيجة
echo ========================================
echo.
echo 🔄 إذا كانت المشكلة في cache:
echo    ├── اضغط Ctrl+Shift+R
echo    ├── أو اضغط Ctrl+F5
echo    ├── أو امسح cache من Developer Tools
echo    └── أعد تحميل الصفحة
echo.
echo 🔄 إذا كانت المشكلة في الذاكرة:
echo    ├── أغلق التطبيق تماماً
echo    ├── أنهِ جميع عمليات Electron
echo    ├── انتظر 10 ثوانِ
echo    └── شغل التطبيق من جديد
echo.
echo 🔄 إذا كانت المشكلة في الملف:
echo    ├── تحقق من أن main.js يشير للملف الصحيح
echo    ├── انسخ الملف المُحدث يدوياً
echo    ├── تأكد من عدم وجود ملفات متضاربة
echo    └── أعد بناء التطبيق إذا لزم الأمر
echo.
echo ========================================
echo    📝 تقرير التشخيص
echo ========================================
echo.
echo بعد إجراء الفحوصات، اكتب تقرير:
echo.
echo 1. هل وجدت "if (!tripDateField.value)" في Sources؟ نعم/لا
echo 2. هل وجدت "if (!tripDateField.value)" في showAddTripModal؟ نعم/لا  
echo 3. هل وجدت "isHomePage" في updateStats؟ نعم/لا
echo 4. هل حقل التاريخ موجود؟ نعم/لا
echo 5. هل التاريخ يُحفظ عند إعادة فتح النموذج؟ نعم/لا
echo.
echo هذا التقرير سيساعد في تحديد الحل الصحيح.
echo.
pause
