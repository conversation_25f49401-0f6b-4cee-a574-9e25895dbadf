// Simple Database Manager - للتوزيع بدون SQLite3

export interface Truck {
  id: number
  plate_number: string
  driver_name: string
  status: 'active' | 'maintenance' | 'inactive'
  notes?: string
  created_at: string
  updated_at: string
}

export interface Trip {
  id: number
  truck_id: number
  trip_date: string
  serial_number?: string
  material_type: string
  quantity_ton: number
  price_per_ton: number
  subtotal: number
  tax: number
  total: number
  loading_location: string
  unloading_location: string
  notes?: string
  created_at: string
  updated_at: string
  // Joined fields
  plate_number?: string
  driver_name?: string
}

export interface Expense {
  id: number
  truck_id: number
  expense_date: string
  expense_type: 'fuel' | 'maintenance' | 'oil_change' | 'driver_salary' | 'daily_allowance' | 'other'
  amount: number
  description: string
  driver_name?: string
  attachment_url?: string
  notes?: string
  created_at: string
  updated_at: string
  // Joined fields
  plate_number?: string
}

export class SimpleDatabaseManager {
  private trucks: Truck[] = []
  private trips: Trip[] = []
  private expenses: Expense[] = []
  private nextId = 1

  constructor() {
    this.initializeSampleData()
  }

  async initialize(): Promise<void> {
    console.log('Simple Database initialized successfully')
    return Promise.resolve()
  }

  private initializeSampleData(): void {
    // Sample trucks
    this.trucks = [
      {
        id: 1,
        plate_number: 'أ ب ج 1234',
        driver_name: 'أحمد محمد',
        status: 'active',
        notes: 'شاحنة جديدة',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        plate_number: 'د هـ و 5678',
        driver_name: 'محمد علي',
        status: 'active',
        notes: 'شاحنة قديمة',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 3,
        plate_number: 'ز ح ط 9012',
        driver_name: 'علي أحمد',
        status: 'maintenance',
        notes: 'في الصيانة',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    // Sample trips
    this.trips = [
      {
        id: 1,
        truck_id: 1,
        trip_date: '2024-01-15',
        material_type: '3/4',
        quantity_ton: 25.5,
        price_per_ton: 150,
        subtotal: 3825,
        tax: 573.75,
        total: 4398.75,
        loading_location: 'الرياض',
        unloading_location: 'جدة',
        notes: 'رحلة تجريبية',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        truck_id: 2,
        trip_date: '2024-01-16',
        material_type: 'Base Course',
        quantity_ton: 30,
        price_per_ton: 120,
        subtotal: 3600,
        tax: 540,
        total: 4140,
        loading_location: 'الدمام',
        unloading_location: 'الرياض',
        notes: 'رحلة عادية',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    // Sample expenses
    this.expenses = [
      {
        id: 1,
        truck_id: 1,
        expense_date: '2024-01-15',
        expense_type: 'fuel',
        amount: 500,
        description: 'تعبئة وقود',
        driver_name: 'أحمد محمد',
        notes: 'وقود للرحلة',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        truck_id: 2,
        expense_date: '2024-01-16',
        expense_type: 'maintenance',
        amount: 800,
        description: 'صيانة دورية',
        driver_name: 'محمد علي',
        notes: 'تغيير زيت وفلاتر',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    this.nextId = 4
  }

  // Trucks CRUD operations
  async getTrucks(): Promise<Truck[]> {
    return Promise.resolve([...this.trucks])
  }

  async addTruck(truck: Omit<Truck, 'id' | 'created_at' | 'updated_at'>): Promise<Truck> {
    const newTruck: Truck = {
      id: this.nextId++,
      ...truck,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    this.trucks.push(newTruck)
    return Promise.resolve(newTruck)
  }

  async updateTruck(id: number, truck: Partial<Omit<Truck, 'id' | 'created_at' | 'updated_at'>>): Promise<Truck | null> {
    const index = this.trucks.findIndex(t => t.id === id)
    if (index === -1) return Promise.resolve(null)

    this.trucks[index] = {
      ...this.trucks[index],
      ...truck,
      updated_at: new Date().toISOString()
    }
    return Promise.resolve(this.trucks[index])
  }

  async deleteTruck(id: number): Promise<boolean> {
    const index = this.trucks.findIndex(t => t.id === id)
    if (index === -1) return Promise.resolve(false)

    this.trucks.splice(index, 1)
    return Promise.resolve(true)
  }

  // Trips CRUD operations
  async getTrips(filters?: any): Promise<Trip[]> {
    const tripsWithTruckInfo = this.trips.map(trip => {
      const truck = this.trucks.find(t => t.id === trip.truck_id)
      return {
        ...trip,
        plate_number: truck?.plate_number,
        driver_name: truck?.driver_name
      }
    })
    return Promise.resolve(tripsWithTruckInfo)
  }

  async addTrip(trip: Omit<Trip, 'id' | 'created_at' | 'updated_at'>): Promise<Trip> {
    console.log('🔍 BACKEND - Received trip data:', trip)
    console.log('🔍 BACKEND - trip_date received:', trip.trip_date)
    console.log('🔍 BACKEND - trip_date type:', typeof trip.trip_date)
    console.log('🔍 BACKEND - Current date for comparison:', new Date().toISOString().split('T')[0])

    const newTrip: Trip = {
      id: this.nextId++,
      ...trip,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    console.log('🔍 BACKEND - Created trip object:', newTrip)
    console.log('🔍 BACKEND - newTrip.trip_date:', newTrip.trip_date)

    this.trips.push(newTrip)

    console.log('🔍 BACKEND - Trip added to array')
    console.log('🔍 BACKEND - Last trip in array:', this.trips[this.trips.length - 1])
    console.log('🔍 BACKEND - Last trip date:', this.trips[this.trips.length - 1].trip_date)

    return Promise.resolve(newTrip)
  }

  async updateTrip(id: number, trip: Partial<Omit<Trip, 'id' | 'created_at' | 'updated_at'>>): Promise<Trip | null> {
    const index = this.trips.findIndex(t => t.id === id)
    if (index === -1) return Promise.resolve(null)

    this.trips[index] = {
      ...this.trips[index],
      ...trip,
      updated_at: new Date().toISOString()
    }
    return Promise.resolve(this.trips[index])
  }

  async deleteTrip(id: number): Promise<boolean> {
    const index = this.trips.findIndex(t => t.id === id)
    if (index === -1) return Promise.resolve(false)

    this.trips.splice(index, 1)
    return Promise.resolve(true)
  }

  // Expenses CRUD operations
  async getExpenses(filters?: any): Promise<Expense[]> {
    const expensesWithTruckInfo = this.expenses.map(expense => {
      const truck = this.trucks.find(t => t.id === expense.truck_id)
      return {
        ...expense,
        plate_number: truck?.plate_number
      }
    })
    return Promise.resolve(expensesWithTruckInfo)
  }

  async addExpense(expense: Omit<Expense, 'id' | 'created_at' | 'updated_at'>): Promise<Expense> {
    const newExpense: Expense = {
      id: this.nextId++,
      ...expense,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    this.expenses.push(newExpense)
    return Promise.resolve(newExpense)
  }

  async updateExpense(id: number, expense: Partial<Omit<Expense, 'id' | 'created_at' | 'updated_at'>>): Promise<Expense | null> {
    const index = this.expenses.findIndex(e => e.id === id)
    if (index === -1) return Promise.resolve(null)

    this.expenses[index] = {
      ...this.expenses[index],
      ...expense,
      updated_at: new Date().toISOString()
    }
    return Promise.resolve(this.expenses[index])
  }

  async deleteExpense(id: number): Promise<boolean> {
    const index = this.expenses.findIndex(e => e.id === id)
    if (index === -1) return Promise.resolve(false)

    this.expenses.splice(index, 1)
    return Promise.resolve(true)
  }

  // Reports
  async getReports(type: string, filters?: any): Promise<any[]> {
    switch (type) {
      case 'monthly':
        return this.getMonthlyReport(filters)
      case 'profitability':
        return this.getProfitabilityReport(filters)
      case 'truck_comparison':
        return this.getTruckComparisonReport(filters)
      case 'material_analysis':
        return this.getMaterialAnalysisReport(filters)
      case 'location_analysis':
        return this.getLocationAnalysisReport(filters)
      default:
        return Promise.resolve([])
    }
  }

  private async getMonthlyReport(filters?: any): Promise<any[]> {
    return Promise.resolve(this.trucks.map(truck => {
      const truckTrips = this.trips.filter(t => t.truck_id === truck.id)
      const truckExpenses = this.expenses.filter(e => e.truck_id === truck.id)
      const totalRevenue = truckTrips.reduce((sum, trip) => sum + trip.total, 0)
      const totalExpenses = truckExpenses.reduce((sum, expense) => sum + expense.amount, 0)

      return {
        truck_id: truck.id,
        plate_number: truck.plate_number,
        driver_name: truck.driver_name,
        total_trips: truckTrips.length,
        total_quantity: truckTrips.reduce((sum, trip) => sum + trip.quantity_ton, 0),
        total_revenue: totalRevenue,
        total_expenses: totalExpenses,
        net_profit: totalRevenue - totalExpenses
      }
    }))
  }

  private async getProfitabilityReport(filters?: any): Promise<any[]> {
    return Promise.resolve([
      { date: '2024-01-15', daily_revenue: 4398.75, daily_expenses: 500, daily_profit: 3898.75 },
      { date: '2024-01-16', daily_revenue: 4140, daily_expenses: 800, daily_profit: 3340 }
    ])
  }

  private async getTruckComparisonReport(filters?: any): Promise<any[]> {
    return this.getMonthlyReport(filters)
  }

  private async getMaterialAnalysisReport(filters?: any): Promise<any[]> {
    const materialStats = new Map()
    this.trips.forEach(trip => {
      if (!materialStats.has(trip.material_type)) {
        materialStats.set(trip.material_type, {
          material_type: trip.material_type,
          trip_count: 0,
          total_quantity: 0,
          total_revenue: 0
        })
      }
      const stats = materialStats.get(trip.material_type)
      stats.trip_count++
      stats.total_quantity += trip.quantity_ton
      stats.total_revenue += trip.total
    })
    return Promise.resolve(Array.from(materialStats.values()))
  }

  private async getLocationAnalysisReport(filters?: any): Promise<any[]> {
    const locationStats = new Map()
    this.trips.forEach(trip => {
      const key = `${trip.loading_location}-${trip.unloading_location}`
      if (!locationStats.has(key)) {
        locationStats.set(key, {
          loading_location: trip.loading_location,
          unloading_location: trip.unloading_location,
          trip_count: 0,
          total_quantity: 0,
          total_revenue: 0
        })
      }
      const stats = locationStats.get(key)
      stats.trip_count++
      stats.total_quantity += trip.quantity_ton
      stats.total_revenue += trip.total
    })
    return Promise.resolve(Array.from(locationStats.values()))
  }
}
