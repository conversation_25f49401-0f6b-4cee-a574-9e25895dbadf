@echo off
echo.
echo 🚛 فتح نظام إدارة الشاحنات...
echo.
echo ⚡ جاري تشغيل التطبيق...
echo.

cd /d "%~dp0"
start "" npx electron resources/app

echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 📋 تعليمات الاختبار:
echo.
echo 🧪 لاختبار الإصلاح الجديد:
echo.
echo 1️⃣ انقر "إضافة شاحنة"
echo 2️⃣ املأ البيانات المطلوبة
echo 3️⃣ انقر "إضافة الشاحنة"
echo 4️⃣ راقب إغلاق النافذة تلقائياً خلال نصف ثانية ✅
echo.
echo 💰 نفس الخطوات لاختبار "إضافة مصروف"
echo.
echo 🎯 إذا أُغلقت النافذة تلقائياً = الإصلاح نجح! ✅
echo 🐛 إذا لم تُغلق = هناك مشكلة تحتاج إصلاح ❌
echo.
pause
