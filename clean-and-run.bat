@echo off
chcp 65001 >nul
echo ========================================
echo    تنظيف وتشغيل نظام إدارة الشاحنات
echo ========================================
echo.

cd /d "C:\TrucksProject"

echo الخطوة 1: تنظيف الملفات القديمة...
if exist "dist" (
    rmdir /s /q dist
    echo تم حذف مجلد dist القديم
)

echo.
echo الخطوة 2: إنشاء مجلد dist جديد...
mkdir dist
mkdir dist\database

echo.
echo الخطوة 3: بناء الملفات الجديدة...
npx tsc electron/main.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node
npx tsc electron/preload.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node
npx tsc electron/database/MockDatabaseManager.ts --outDir dist --target ES2020 --module CommonJS --esModuleInterop --skipLibCheck --resolveJsonModule --moduleResolution node

echo.
echo الخطوة 4: التحقق من الملفات...
if exist "dist\main.js" (
    echo ✓ main.js تم إنشاؤه
) else (
    echo ❌ main.js لم يتم إنشاؤه
    pause
    exit /b 1
)

if exist "dist\preload.js" (
    echo ✓ preload.js تم إنشاؤه
) else (
    echo ❌ preload.js لم يتم إنشاؤه
    pause
    exit /b 1
)

if exist "dist\database\MockDatabaseManager.js" (
    echo ✓ MockDatabaseManager.js تم إنشاؤه
) else (
    echo ❌ MockDatabaseManager.js لم يتم إنشاؤه
    pause
    exit /b 1
)

echo.
echo الخطوة 5: تشغيل التطبيق...
echo سيفتح التطبيق الآن...
npm start

pause
