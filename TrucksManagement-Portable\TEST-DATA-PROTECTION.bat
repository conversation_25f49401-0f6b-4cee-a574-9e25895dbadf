@echo off
echo.
echo ========================================
echo 🛡️ اختبار حماية سلامة البيانات
echo ========================================
echo.
echo 🆕 الميزة الجديدة:
echo    ✅ منع حذف الشاحنات المرتبطة ببيانات
echo    ✅ رسائل تحذيرية واضحة
echo    ✅ خيار الحذف القسري للمتقدمين
echo    ✅ حماية شاملة من فقدان البيانات
echo.
echo 📋 خطة الاختبار:
echo.
echo 1️⃣  سيتم تشغيل التطبيق
echo 2️⃣  إنشاء شاحنة جديدة
echo 3️⃣  إضافة رحلة ومصروف لها
echo 4️⃣  اختبار محاولة حذف الشاحنة
echo 5️⃣  اختبار الحذف القسري
echo.
pause

echo 📱 تشغيل التطبيق...
start "" npx electron resources/app

echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 🔍 دليل اختبار حماية البيانات:
echo.
echo ==========================================
echo 🚛 المرحلة الأولى: إنشاء البيانات
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. انتقل لصفحة "الشاحنات"
echo    2. انقر "إضافة شاحنة جديدة"
echo    3. املأ البيانات:
echo       - رقم اللوحة: تست 9999
echo       - اسم السائق: سائق الاختبار
echo       - الحالة: نشطة
echo    4. انقر "إضافة"
echo.
echo 🚚 إضافة رحلة:
echo    1. انتقل لصفحة "الرحلات"
echo    2. انقر "إضافة رحلة جديدة"
echo    3. اختر الشاحنة: تست 9999
echo    4. املأ باقي البيانات
echo    5. انقر "تسجيل الرحلة"
echo.
echo 💰 إضافة مصروف:
echo    1. انتقل لصفحة "المصروفات"
echo    2. انقر "إضافة مصروف جديد"
echo    3. اختر الشاحنة: تست 9999
echo    4. املأ باقي البيانات
echo    5. انقر "إضافة المصروف"
echo.
echo ==========================================
echo 🛡️ المرحلة الثانية: اختبار الحماية
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. ارجع لصفحة "الشاحنات"
echo    2. ابحث عن الشاحنة "تست 9999"
echo    3. انقر على زر الحذف 🗑️ بجانبها
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ يجب أن تظهر رسالة تحذيرية تحتوي على:
echo       - "❌ لا يمكن حذف الشاحنة"
echo       - "🚚 1 رحلة مسجلة"
echo       - "💰 1 مصروف مسجل"
echo       - "🛡️ لحماية سلامة البيانات، يجب أولاً"
echo       - خطوات الحذف الآمن
echo       - "💡 هذا يضمن عدم فقدان أي بيانات مهمة"
echo.
echo ==========================================
echo 🛡️ المرحلة الثالثة: التحقق من الحماية المطلقة
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. في الرسالة السابقة، انقر "موافق"
echo    2. يجب أن تختفي الرسالة
echo    3. تحقق من أن الشاحنة لا تزال موجودة
echo    4. تحقق من أن جميع البيانات سليمة
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ يجب أن يحدث التالي:
echo       - الشاحنة "تست 9999" لا تزال موجودة
echo       - الرحلة المرتبطة لا تزال موجودة
echo       - المصروف المرتبط لا يزال موجود
echo       - لا يتم حذف أي بيانات
echo       - رسالة تحذيرية في الإشعارات
echo.
echo ==========================================
echo 🔒 المرحلة الرابعة: اختبار الحذف الآمن
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. احذف الرحلة المرتبطة بالشاحنة "تست 9999" يدوياً
echo    2. احذف المصروف المرتبط بالشاحنة "تست 9999" يدوياً
echo    3. الآن حاول حذف الشاحنة "تست 9999"
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ يجب أن يحدث التالي:
echo       - رسالة تأكيد عادية
echo       - "✅ الشاحنة غير مرتبطة بأي رحلات أو مصروفات"
echo       - حذف فوري وآمن للشاحنة
echo.
echo ==========================================
echo 🔒 المرحلة الخامسة: اختبار الحذف العادي
echo ==========================================
echo.
echo 📍 الخطوات:
echo    1. أضف شاحنة جديدة أخرى
echo       - رقم اللوحة: تست 8888
echo       - اسم السائق: سائق نظيف
echo    2. لا تضف أي رحلات أو مصروفات لها
echo    3. احذف الشاحنة مباشرة
echo.
echo 🎯 النتيجة المتوقعة:
echo    ✅ يجب أن يحدث التالي:
echo       - رسالة تأكيد عادية
echo       - "✅ الشاحنة غير مرتبطة بأي رحلات أو مصروفات"
echo       - حذف فوري بدون مشاكل
echo.
echo 🔬 اختبار العدادات:
echo    1. راقب العدادات في الأعلى
echo    2. تحقق من تحديثها بعد كل عملية
echo    3. تحقق من تطابقها مع البيانات الفعلية
echo.
echo ==========================================
echo 📊 تقييم النتائج
echo ==========================================
echo.
echo ✅ الاختبار ناجح إذا:
echo    - منع حذف الشاحنات المرتبطة ببيانات نهائياً
echo    - ظهور رسائل تحذيرية واضحة
echo    - عدم وجود خيار حذف قسري
echo    - الحماية المطلقة تعمل 100%%
echo    - الحذف الآمن يعمل بعد إزالة البيانات المرتبطة
echo    - تحديث العدادات تلقائياً
echo    - عدم وجود أخطاء في الكونسول
echo.
echo ❌ الاختبار فاشل إذا:
echo    - تم حذف الشاحنة رغم وجود بيانات مرتبطة
echo    - لم تظهر رسائل التحذير
echo    - وجود خيار حذف قسري
echo    - إمكانية تجاوز الحماية
echo    - ظهور أخطاء في الكونسول
echo.
echo 🎉 إذا نجحت جميع الاختبارات:
echo    - النظام محمي بالكامل من فقدان البيانات
echo    - تجربة مستخدم ممتازة
echo    - جاهز للاستخدام الفعلي
echo.
pause
