{"name": "trucks-management-system", "version": "2.0.0", "description": "نظام إدارة الشاحنات المتقدم مع قاعدة بيانات SQLite - الإصدار التجاري", "main": "main.js", "homepage": "https://trucksmanagement.com", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-all": "electron-builder --win --mac --linux", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "rebuild": "electron-rebuild", "prepare-build": "npm install && npm run rebuild"}, "keywords": ["trucks", "management", "sqlite", "electron", "desktop", "commercial", "business", "fleet", "logistics"], "author": {"name": "شركة إدارة الشاحنات المتقدمة", "email": "<EMAIL>", "url": "https://trucksmanagement.com"}, "license": "Commercial", "copyright": "© 2024 شركة إدارة الشاحنات المتقدمة. جميع الحقوق محفوظة.", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "electron-rebuild": "^3.2.9"}, "dependencies": {"better-sqlite3": "^9.2.2", "electron-store": "^8.1.0"}, "build": {"appId": "com.trucksmanagement.professional", "productName": "نظام إدارة الشاحنات المحترف", "copyright": "© 2024 شركة إدارة الشاحنات المتقدمة", "directories": {"output": "dist", "buildResources": "build"}, "files": ["main.js", "package.json", "resources/**/*", "data/**/*", "node_modules/**/*", "!node_modules/.cache", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "data", "to": "data", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "requestedExecutionLevel": "asInvoker", "publisherName": "شركة إدارة الشاحنات المتقدمة", "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "perMachine": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة الشاحنات المحترف", "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "installerHeaderIcon": "build/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Business", "include": "build/installer.nsh", "artifactName": "TrucksManagement-Professional-Setup-${version}.${ext}", "displayLanguageSelector": false, "installerLanguages": ["ar", "en"], "language": "ar"}, "portable": {"artifactName": "TrucksManagement-Professional-Portable-${version}.${ext}"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "category": "public.app-category.business", "artifactName": "TrucksManagement-Professional-${version}-mac.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "build/icon.png", "category": "Office", "artifactName": "TrucksManagement-Professional-${version}-linux.${ext}"}, "publish": {"provider": "generic", "url": "https://releases.trucksmanagement.com/"}}}