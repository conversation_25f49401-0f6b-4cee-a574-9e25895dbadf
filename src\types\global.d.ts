export interface ElectronAPI {
  // Trucks
  getTrucks: () => Promise<any[]>
  addTruck: (truck: any) => Promise<any>
  updateTruck: (id: number, truck: any) => Promise<any>
  deleteTruck: (id: number) => Promise<boolean>
  
  // Trips
  getTrips: (filters?: any) => Promise<any[]>
  addTrip: (trip: any) => Promise<any>
  updateTrip: (id: number, trip: any) => Promise<any>
  deleteTrip: (id: number) => Promise<boolean>
  
  // Expenses
  getExpenses: (filters?: any) => Promise<any[]>
  addExpense: (expense: any) => Promise<any>
  updateExpense: (id: number, expense: any) => Promise<any>
  deleteExpense: (id: number) => Promise<boolean>
  
  // Reports
  getReports: (type: string, filters?: any) => Promise<any>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

export {}
