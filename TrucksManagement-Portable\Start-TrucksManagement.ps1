# Trucks Management System Launcher
Write-Host "========================================" -ForegroundColor Green
Write-Host "    Trucks Management System Launcher" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Change to script directory
Set-Location $PSScriptRoot

Write-Host "[1/3] Preparing to launch..." -ForegroundColor Yellow
Write-Host ""

# Check if electron is already installed locally
if (Test-Path "node_modules\.bin\electron.cmd") {
    Write-Host "[2/3] Using local Electron installation..." -ForegroundColor Green
    Write-Host "[3/3] Starting application..." -ForegroundColor Green
    Write-Host ""
    Start-Process "node_modules\.bin\electron.cmd" -ArgumentList "resources\app"
    Write-Host "Application started! Check for new window." -ForegroundColor Green
} else {
    Write-Host "[2/3] First time setup - Installing Electron..." -ForegroundColor Yellow
    Write-Host "This will take 1-3 minutes. Please wait..." -ForegroundColor Yellow
    Write-Host ""

    # Install electron locally
    try {
        npm install electron
        Write-Host ""
        Write-Host "[3/3] Installation complete! Starting application..." -ForegroundColor Green
        Write-Host ""

        if (Test-Path "node_modules\.bin\electron.cmd") {
            Start-Process "node_modules\.bin\electron.cmd" -ArgumentList "resources\app"
            Write-Host "Application started! Check for new window." -ForegroundColor Green
        } else {
            Write-Host "Trying alternative method..." -ForegroundColor Yellow
            Start-Process "npx" -ArgumentList "electron", "resources\app"
        }
    } catch {
        Write-Host "Installation failed. Trying direct method..." -ForegroundColor Red
        Start-Process "npx" -ArgumentList "electron", "resources\app"
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "If the application window doesn't appear:" -ForegroundColor Yellow
Write-Host "1. Check Windows taskbar" -ForegroundColor White
Write-Host "2. Try running as administrator" -ForegroundColor White
Write-Host "3. Make sure Node.js is installed" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to continue"
