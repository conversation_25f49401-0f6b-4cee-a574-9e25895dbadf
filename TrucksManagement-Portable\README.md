# 🚛 نظام إدارة الشاحنات - النسخة المحمولة

## 🎉 مرحباً بك في نظام إدارة الشاحنات!

هذا نظام شامل لإدارة شاحنات النقل الثقيل مع واجهة عربية كاملة.

## 🚀 كيفية التشغيل

### الطريقة الأولى (الأسهل):
انقر نقراً مزدوجاً على ملف: **`RUN.bat`**

### الطريقة الثانية:
انقر نقراً مزدوجاً على ملف: **`Start-TrucksManagement.bat`**

### الطريقة الثالثة (PowerShell):
انقر بالزر الأيمن على **`Start-TrucksManagement.ps1`** واختر "Run with PowerShell"

### الطريقة الرابعة (يدوي):
1. تأكد من وجود Node.js على النظام
2. افتح Command Prompt في هذا المجلد
3. شغل الأمر: `npx electron resources/app`

## 📋 المتطلبات

- **Windows 10/11** (64-bit)
- **Node.js 16+** (للتشغيل)
- **4 GB RAM** (الحد الأدنى)
- **100 MB** مساحة فارغة

## 🎯 المميزات

### ✅ إدارة الشاحنات:
- إضافة وتعديل وحذف الشاحنات
- تتبع حالة كل شاحنة (نشطة، صيانة، متوقفة)
- معلومات السائقين والملاحظات

### ✅ تسجيل الرحلات:
- تسجيل مفصل لكل رحلة
- حساب تلقائي للأسعار والضرائب
- تتبع المواد المنقولة والمواقع

### ✅ إدارة المصروفات:
- تسجيل جميع أنواع المصروفات
- ربط المصروفات بالشاحنات
- تصنيف المصروفات (وقود، صيانة، رواتب، إلخ)

### ✅ التقارير والتحليلات:
- تقارير شهرية شاملة
- تحليل الربحية
- مقارنة أداء الشاحنات
- تحليل المواد والمواقع

### ✅ قاعدة البيانات:
- قاعدة بيانات مدمجة (لا تحتاج خادم)
- بيانات تجريبية جاهزة للاختبار
- حفظ تلقائي لجميع البيانات

## 📊 البيانات التجريبية

النظام يأتي مع بيانات تجريبية جاهزة:

### الشاحنات:
- **أ ب ج 1234** - أحمد محمد (نشطة)
- **د هـ و 5678** - محمد علي (نشطة)  
- **ز ح ط 9012** - علي أحمد (في الصيانة)

### الرحلات:
- رحلة من الرياض إلى جدة (مادة 3/4)
- رحلة من الدمام إلى الرياض (Base Course)

### المصروفات:
- تعبئة وقود - 500 ريال
- صيانة دورية - 800 ريال

## 🔧 استكشاف الأخطاء

### إذا لم يعمل التطبيق:
1. تأكد من وجود Node.js: `node --version`
2. شغل من Command Prompt: `npx electron resources/app`
3. تحقق من رسائل الخطأ في الكونسول

### إذا ظهرت صفحة بيضاء:
1. انتظر قليلاً (قد يحتاج وقت للتحميل)
2. أعد تشغيل التطبيق
3. تحقق من ملفات HTML في مجلد resources/app/dist/renderer

### إذا لم تعمل قاعدة البيانات:
- النظام يستخدم قاعدة بيانات في الذاكرة
- البيانات ستعود للحالة الافتراضية عند إعادة التشغيل
- هذا طبيعي في النسخة التجريبية

## 📁 هيكل الملفات

```
TrucksManagement-Portable/
├── نظام إدارة الشاحنات.bat    # ملف التشغيل
├── README.md                    # هذا الملف
└── resources/
    └── app/
        ├── package.json         # إعدادات التطبيق
        └── dist/
            ├── main.js          # الملف الرئيسي
            ├── preload.js       # ملف الوسطاء
            ├── database/        # قاعدة البيانات
            └── renderer/        # الواجهة
```

## 🎨 الواجهة

- **لغة عربية كاملة** مع دعم RTL
- **تصميم متجاوب** يعمل على جميع الأحجام
- **ألوان متناسقة** وسهلة على العين
- **أيقونات واضحة** لسهولة الاستخدام

## 📞 الدعم

هذا نظام تجريبي للاختبار والتطوير.

### للمطورين:
- الكود مفتوح المصدر
- يمكن التعديل والتطوير
- مبني بـ Electron + TypeScript

### للمستخدمين:
- النظام جاهز للاستخدام الفوري
- لا يحتاج خبرة تقنية
- واجهة بسيطة وسهلة

## 🏆 الإصدار

**الإصدار:** 1.0.0  
**تاريخ الإنشاء:** 2024  
**النوع:** نسخة محمولة (Portable)  
**الحجم:** ~50-100 MB  

---

**🎉 استمتع باستخدام نظام إدارة الشاحنات!** 🚛✨
