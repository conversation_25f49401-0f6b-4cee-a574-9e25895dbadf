@echo off
echo ========================================
echo    نظام إدارة الشاحنات - تشغيل مباشر
echo ========================================
echo.

cd /d "C:\TrucksProject"

echo الخطوة 1: بناء الملفات الأساسية...
call npm run build:main
if errorlevel 1 (
    echo خطأ في البناء!
    pause
    exit /b 1
)

echo الخطوة 2: تشغيل التطبيق...
echo سيفتح التطبيق الآن...
start "" npm run electron

echo الخطوة 3: تشغيل خادم التطوير...
timeout /t 3 /nobreak >nul
call npm run dev:renderer

pause
