@echo off
echo ========================================
echo    تشخيص مشاكل نظام إدارة الشاحنات
echo ========================================
echo.

echo فحص النظام...
echo.

echo 1. إصدار Windows:
ver

echo.
echo 2. فحص Node.js:
where node
node --version 2>nul
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 💡 الحل: حمل من https://nodejs.org/
) else (
    echo ✅ Node.js مثبت
)

echo.
echo 3. فحص npm:
where npm
npm --version 2>nul
if errorlevel 1 (
    echo ❌ npm غير متوفر
) else (
    echo ✅ npm متوفر
)

echo.
echo 4. فحص مجلد المشروع:
if exist "C:\TrucksProject" (
    echo ✅ مجلد المشروع موجود
    dir "C:\TrucksProject" | find "package.json"
    if errorlevel 1 (
        echo ❌ ملف package.json مفقود
    ) else (
        echo ✅ ملف package.json موجود
    )
) else (
    echo ❌ مجلد المشروع غير موجود
)

echo.
echo 5. فحص node_modules:
if exist "C:\TrucksProject\node_modules" (
    echo ✅ مجلد node_modules موجود
) else (
    echo ❌ مجلد node_modules غير موجود - يحتاج npm install
)

echo.
echo 6. فحص منافذ الشبكة:
netstat -an | find "3000"
if errorlevel 1 (
    echo ✅ المنفذ 3000 متاح
) else (
    echo ⚠️ المنفذ 3000 مستخدم
)

echo.
echo ========================================
echo انتهى التشخيص
echo ========================================
pause
