import { contextBridge, ipcRenderer } from 'electron'

export interface ElectronAPI {
  // Trucks
  getTrucks: () => Promise<any[]>
  addTruck: (truck: any) => Promise<any>
  updateTruck: (id: number, truck: any) => Promise<any>
  deleteTruck: (id: number) => Promise<boolean>
  
  // Trips
  getTrips: (filters?: any) => Promise<any[]>
  addTrip: (trip: any) => Promise<any>
  updateTrip: (id: number, trip: any) => Promise<any>
  deleteTrip: (id: number) => Promise<boolean>
  
  // Expenses
  getExpenses: (filters?: any) => Promise<any[]>
  addExpense: (expense: any) => Promise<any>
  updateExpense: (id: number, expense: any) => Promise<any>
  deleteExpense: (id: number) => Promise<boolean>
  
  // Reports
  getReports: (type: string, filters?: any) => Promise<any>
}

const electronAPI: ElectronAPI = {
  // Trucks
  getTrucks: () => ipcRenderer.invoke('db:getTrucks'),
  addTruck: (truck) => ipcRenderer.invoke('db:addTruck', truck),
  updateTruck: (id, truck) => ipcRenderer.invoke('db:updateTruck', id, truck),
  deleteTruck: (id) => ipcRenderer.invoke('db:deleteTruck', id),
  
  // Trips
  getTrips: (filters) => ipcRenderer.invoke('db:getTrips', filters),
  addTrip: (trip) => ipcRenderer.invoke('db:addTrip', trip),
  updateTrip: (id, trip) => ipcRenderer.invoke('db:updateTrip', id, trip),
  deleteTrip: (id) => ipcRenderer.invoke('db:deleteTrip', id),
  
  // Expenses
  getExpenses: (filters) => ipcRenderer.invoke('db:getExpenses', filters),
  addExpense: (expense) => ipcRenderer.invoke('db:addExpense', expense),
  updateExpense: (id, expense) => ipcRenderer.invoke('db:updateExpense', id, expense),
  deleteExpense: (id) => ipcRenderer.invoke('db:deleteExpense', id),
  
  // Reports
  getReports: (type, filters) => ipcRenderer.invoke('db:getReports', type, filters),
}

contextBridge.exposeInMainWorld('electronAPI', electronAPI)

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
