@echo off
echo ========================================
echo    تثبيت التبعيات المطلوبة
echo ========================================
cd /d "C:\TrucksProject"

echo الخطوة 1: تثبيت التبعيات الأساسية...
npm install --save sqlite3 react react-dom react-router-dom react-hook-form recharts lucide-react clsx tailwind-merge

echo الخطوة 2: تثبيت تبعيات التطوير...
npm install --save-dev @types/node @types/react @types/react-dom @types/sqlite3 typescript electron electron-builder electron-rebuild vite @vitejs/plugin-react tailwindcss autoprefixer postcss

echo الخطوة 3: تثبيت electron-rebuild عالمياً...
npm install -g electron-rebuild

echo الخطوة 4: إعادة بناء native modules...
npx electron-rebuild

echo تم تثبيت جميع التبعيات بنجاح!
pause
