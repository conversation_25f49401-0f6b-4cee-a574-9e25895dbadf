import React, { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, Search, Filter } from 'lucide-react'
import TripForm from '../components/TripForm'

interface Trip {
  id?: number
  truck_id: number
  trip_date: string
  serial_number?: string
  material_type: string
  quantity_ton: number
  price_per_ton: number
  subtotal: number
  tax: number
  total: number
  loading_location: string
  unloading_location: string
  notes?: string
  plate_number?: string
  driver_name?: string
}

interface Truck {
  id: number
  plate_number: string
  driver_name?: string
  status: string
}

const TripsPage: React.FC = () => {
  const [trips, setTrips] = useState<Trip[]>([])
  const [trucks, setTrucks] = useState<Truck[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingTrip, setEditingTrip] = useState<Trip | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTruck, setSelectedTruck] = useState<string>('')
  const [dateFilter, setDateFilter] = useState({
    start: '',
    end: ''
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [tripsData, trucksData] = await Promise.all([
        window.electronAPI.getTrips(),
        window.electronAPI.getTrucks()
      ])
      setTrips(tripsData)
      setTrucks(trucksData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddTrip = () => {
    setEditingTrip(null)
    setShowForm(true)
  }

  const handleEditTrip = (trip: Trip) => {
    setEditingTrip(trip)
    setShowForm(true)
  }

  const handleDeleteTrip = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الرحلة؟')) {
      try {
        await window.electronAPI.deleteTrip(id)
        await loadData()
      } catch (error) {
        console.error('Error deleting trip:', error)
        alert('حدث خطأ أثناء حذف الرحلة')
      }
    }
  }

  const handleFormSubmit = async (tripData: Omit<Trip, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      console.log('🔍 REACT - Form submitted with data:', tripData)
      console.log('🔍 REACT - trip_date received:', tripData.trip_date)
      console.log('🔍 REACT - trip_date type:', typeof tripData.trip_date)
      console.log('🔍 REACT - Current date for comparison:', new Date().toISOString().split('T')[0])

      if (editingTrip) {
        console.log('🔍 REACT - Updating existing trip')
        await window.electronAPI.updateTrip(editingTrip.id!, tripData)
      } else {
        console.log('🔍 REACT - Adding new trip')
        const result = await window.electronAPI.addTrip(tripData)
        console.log('🔍 REACT - Backend returned:', result)
        console.log('🔍 REACT - Backend trip_date:', result?.trip_date)
      }
      await loadData()
      setShowForm(false)
      setEditingTrip(null)
    } catch (error) {
      console.error('Error saving trip:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const filteredTrips = trips.filter(trip => {
    const matchesSearch = 
      trip.plate_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.material_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.loading_location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.unloading_location.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTruck = !selectedTruck || trip.truck_id.toString() === selectedTruck

    const matchesDate = 
      (!dateFilter.start || trip.trip_date >= dateFilter.start) &&
      (!dateFilter.end || trip.trip_date <= dateFilter.end)

    return matchesSearch && matchesTruck && matchesDate
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">إدارة الرحلات</h1>
        <button
          onClick={handleAddTrip}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          إضافة رحلة جديدة
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="البحث..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pr-10"
              />
            </div>
            
            <select
              value={selectedTruck}
              onChange={(e) => setSelectedTruck(e.target.value)}
              className="input"
            >
              <option value="">جميع الشاحنات</option>
              {trucks.map(truck => (
                <option key={truck.id} value={truck.id}>
                  {truck.plate_number} - {truck.driver_name || 'بدون سائق'}
                </option>
              ))}
            </select>

            <input
              type="date"
              placeholder="من تاريخ"
              value={dateFilter.start}
              onChange={(e) => setDateFilter(prev => ({ ...prev, start: e.target.value }))}
              className="input"
            />

            <input
              type="date"
              placeholder="إلى تاريخ"
              value={dateFilter.end}
              onChange={(e) => setDateFilter(prev => ({ ...prev, end: e.target.value }))}
              className="input"
            />
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{filteredTrips.length}</p>
              <p className="text-sm text-gray-600">إجمالي الرحلات</p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {filteredTrips.reduce((sum, trip) => sum + trip.quantity_ton, 0).toFixed(1)}
              </p>
              <p className="text-sm text-gray-600">إجمالي الكمية (طن)</p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-emerald-600">
                {formatCurrency(filteredTrips.reduce((sum, trip) => sum + trip.total, 0))}
              </p>
              <p className="text-sm text-gray-600">إجمالي الإيرادات</p>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {filteredTrips.length > 0 
                  ? formatCurrency(filteredTrips.reduce((sum, trip) => sum + trip.total, 0) / filteredTrips.length)
                  : formatCurrency(0)
                }
              </p>
              <p className="text-sm text-gray-600">متوسط الإيراد</p>
            </div>
          </div>
        </div>
      </div>

      {/* Trips Table */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الشاحنة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  نوع المادة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الكمية (طن)
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  سعر الطن
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجمالي
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التحميل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التنزيل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTrips.map((trip) => (
                <tr key={trip.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(trip.trip_date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{trip.plate_number}</div>
                      <div className="text-gray-500">{trip.driver_name || 'بدون سائق'}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {trip.material_type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {trip.quantity_ton}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(trip.price_per_ton)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                    {formatCurrency(trip.total)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {trip.loading_location}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {trip.unloading_location}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEditTrip(trip)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteTrip(trip.id!)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredTrips.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد رحلات مسجلة</p>
            </div>
          )}
        </div>
      </div>

      {/* Trip Form Modal */}
      {showForm && (
        <TripForm
          trip={editingTrip}
          trucks={trucks}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false)
            setEditingTrip(null)
          }}
        />
      )}
    </div>
  )
}

export default TripsPage
