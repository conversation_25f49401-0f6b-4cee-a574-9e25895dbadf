import React from 'react'
import { useForm } from 'react-hook-form'
import { X } from 'lucide-react'

interface Truck {
  id?: number
  plate_number: string
  driver_name?: string
  status: 'active' | 'maintenance' | 'inactive'
  notes?: string
}

interface TruckFormProps {
  truck?: Truck | null
  onSubmit: (data: Omit<Truck, 'id' | 'created_at' | 'updated_at'>) => void
  onCancel: () => void
}

const TruckForm: React.FC<TruckFormProps> = ({ truck, onSubmit, onCancel }) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<Omit<Truck, 'id' | 'created_at' | 'updated_at'>>({
    defaultValues: {
      plate_number: truck?.plate_number || '',
      driver_name: truck?.driver_name || '',
      status: truck?.status || 'active',
      notes: truck?.notes || ''
    }
  })

  const handleFormSubmit = (data: Omit<Truck, 'id' | 'created_at' | 'updated_at'>) => {
    onSubmit(data)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {truck ? 'تعديل الشاحنة' : 'إضافة شاحنة جديدة'}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              رقم اللوحة *
            </label>
            <input
              type="text"
              {...register('plate_number', {
                required: 'رقم اللوحة مطلوب',
                minLength: {
                  value: 3,
                  message: 'رقم اللوحة يجب أن يكون 3 أحرف على الأقل'
                }
              })}
              className="input"
              placeholder="مثال: أ ب ج 1234"
            />
            {errors.plate_number && (
              <p className="mt-1 text-sm text-red-600">{errors.plate_number.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اسم السائق
            </label>
            <input
              type="text"
              {...register('driver_name')}
              className="input"
              placeholder="اسم السائق (اختياري)"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              حالة الشاحنة *
            </label>
            <select
              {...register('status', { required: 'حالة الشاحنة مطلوبة' })}
              className="input"
            >
              <option value="active">نشطة</option>
              <option value="maintenance">في الصيانة</option>
              <option value="inactive">غير نشطة</option>
            </select>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ملاحظات
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="input resize-none"
              placeholder="ملاحظات إضافية (اختياري)"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline"
              disabled={isSubmitting}
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحفظ...' : truck ? 'تحديث' : 'إضافة'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default TruckForm
