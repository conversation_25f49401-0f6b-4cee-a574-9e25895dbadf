@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔧 إصلاح نهائي لحقول التاريخ
echo ========================================
echo.
echo 🎯 المشكلة: حقول التاريخ لا تظهر
echo 🔍 السبب المحتمل: تضارب بين ملفات مختلفة أو cache
echo ✅ الحل: مزامنة الملفات + مسح cache + إعادة تشغيل
echo.
echo ========================================
echo    🔄 الخطوات التلقائية
echo ========================================
echo.
echo 1️⃣ مزامنة الملفات...

copy "resources\app\trucks-app.html" "resources\app\dist\renderer\trucks-app.html" >nul

if %errorlevel% == 0 (
    echo ✅ تم نسخ الملف بنجاح!
) else (
    echo ❌ فشل في نسخ الملف!
)

echo.
echo 2️⃣ إنشاء نسخة احتياطية...

copy "resources\app\dist\renderer\trucks-app.html" "resources\app\dist\renderer\trucks-app-backup.html" >nul

echo ✅ تم إنشاء نسخة احتياطية

echo.
echo ========================================
echo    🚀 الخطوات اليدوية المطلوبة
echo ========================================
echo.
echo 3️⃣ أغلق التطبيق تماماً:
echo    ├── أغلق جميع نوافذ التطبيق
echo    ├── افتح Task Manager (Ctrl+Shift+Esc)
echo    ├── ابحث عن عمليات "Electron" أو "TrucksManagement"
echo    ├── أنهِ جميع العمليات المتعلقة
echo    └── انتظر 10 ثوانِ
echo.
echo 4️⃣ شغل التطبيق من جديد:
echo    └── استخدم DIRECT-RUN.bat
echo.
echo 5️⃣ امسح cache المتصفح فوراً:
echo    ├── اضغط F12 لفتح Developer Tools
echo    ├── اضغط Ctrl+Shift+R (Hard Reload)
echo    ├── أو اذهب لتبويب Application
echo    ├── اختر Storage → Local Storage
echo    ├── امسح جميع البيانات
echo    └── أعد تحميل الصفحة (F5)
echo.
echo 6️⃣ اختبر حقول التاريخ:
echo    ├── اذهب لصفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── يجب أن ترى حقل "تاريخ الرحلة"
echo    ├── التاريخ الافتراضي = تاريخ اليوم
echo    └── يمكن تعديل التاريخ
echo.
echo 7️⃣ اختبر المصروفات:
echo    ├── اذهب لصفحة المصروفات
echo    ├── اضغط "إضافة مصروف جديد"
echo    ├── يجب أن ترى حقل "تاريخ المصروف"
echo    └── نفس الوظائف كالرحلات
echo.
echo ========================================
echo    🔍 إذا لم تظهر الحقول بعد
echo ========================================
echo.
echo 🛠️ فحص متقدم:
echo    ├── افتح Developer Tools (F12)
echo    ├── اذهب لتبويب Console
echo    ├── اكتب: document.getElementById('trip-date')
echo    ├── إذا ظهر null = الحقل غير موجود
echo    └── إذا ظهر <input> = الحقل موجود لكن مخفي
echo.
echo 🔧 حلول إضافية:
echo    ├── تحقق من عدم وجود أخطاء CSS تخفي الحقول
echo    ├── تأكد من أن JavaScript لا يحذف الحقول
echo    ├── جرب فتح الملف مباشرة في Chrome
echo    └── تحقق من أن الملف الصحيح يتم تحميله
echo.
echo ========================================
echo    📋 معلومات تقنية
echo ========================================
echo.
echo 📁 الملفات المُحدثة:
echo    ├── resources\app\trucks-app.html (الأساسي)
echo    └── resources\app\dist\renderer\trucks-app.html (التشغيل)
echo.
echo 🔧 الحقول المُضافة:
echo    ├── trip-date (نموذج الرحلة)
echo    ├── expense-date (نموذج المصروف)
echo    ├── edit-trip-date (تعديل الرحلة)
echo    └── edit-expense-date (تعديل المصروف)
echo.
echo 💻 الدوال المُحدثة:
echo    ├── showAddTripModal() - تعين التاريخ الافتراضي
echo    ├── showAddExpenseModal() - تعين التاريخ الافتراضي
echo    ├── addTrip() - تقرأ التاريخ من الحقل
echo    └── addExpense() - تقرأ التاريخ من الحقل
echo.
echo 🎯 إذا استمرت المشكلة:
echo    ├── تواصل مع المطور
echo    ├── أرسل screenshot من Developer Tools
echo    └── أرسل رسائل Console إذا وجدت أخطاء
echo.
pause
