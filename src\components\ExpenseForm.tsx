import React from 'react'
import { useForm } from 'react-hook-form'
import { X, Upload } from 'lucide-react'

interface Expense {
  id?: number
  truck_id: number
  expense_date: string
  expense_type: 'fuel' | 'maintenance' | 'oil_change' | 'driver_salary' | 'daily_allowance' | 'other'
  amount: number
  description?: string
  driver_name?: string
  attachment_url?: string
  notes?: string
}

interface Truck {
  id: number
  plate_number: string
  driver_name?: string
  status: string
}

interface ExpenseFormProps {
  expense?: Expense | null
  trucks: Truck[]
  onSubmit: (data: Omit<Expense, 'id' | 'created_at' | 'updated_at'>) => void
  onCancel: () => void
}

const ExpenseForm: React.FC<ExpenseFormProps> = ({ expense, trucks, onSubmit, onCancel }) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<Omit<Expense, 'id' | 'created_at' | 'updated_at'>>({
    defaultValues: {
      truck_id: expense?.truck_id || 0,
      expense_date: expense?.expense_date || '', // لا نعيد تعيين التاريخ - نتركه فارغاً للمستخدم
      expense_type: expense?.expense_type || 'fuel',
      amount: expense?.amount || 0,
      description: expense?.description || '',
      driver_name: expense?.driver_name || '',
      attachment_url: expense?.attachment_url || '',
      notes: expense?.notes || ''
    }
  })

  const handleFormSubmit = (data: Omit<Expense, 'id' | 'created_at' | 'updated_at'>) => {
    onSubmit(data)
  }

  const expenseTypes = [
    { value: 'fuel', label: 'وقود' },
    { value: 'maintenance', label: 'صيانة' },
    { value: 'oil_change', label: 'غيار زيت' },
    { value: 'driver_salary', label: 'راتب سائق' },
    { value: 'daily_allowance', label: 'مصاريف يومية للسائق' },
    { value: 'other', label: 'أخرى' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {expense ? 'تعديل المصروف' : 'إضافة مصروف جديد'}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">معلومات أساسية</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الشاحنة *
                </label>
                <select
                  {...register('truck_id', { 
                    required: 'اختيار الشاحنة مطلوب',
                    valueAsNumber: true 
                  })}
                  className="input"
                >
                  <option value={0}>اختر الشاحنة</option>
                  {trucks.map(truck => (
                    <option key={truck.id} value={truck.id}>
                      {truck.plate_number} - {truck.driver_name || 'بدون سائق'}
                    </option>
                  ))}
                </select>
                {errors.truck_id && (
                  <p className="mt-1 text-sm text-red-600">{errors.truck_id.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  تاريخ المصروف *
                </label>
                <input
                  type="date"
                  {...register('expense_date', { required: 'تاريخ المصروف مطلوب' })}
                  className="input"
                />
                {errors.expense_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.expense_date.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  نوع المصروف *
                </label>
                <select
                  {...register('expense_type', { required: 'نوع المصروف مطلوب' })}
                  className="input"
                >
                  {expenseTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                {errors.expense_type && (
                  <p className="mt-1 text-sm text-red-600">{errors.expense_type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المبلغ (ريال) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  {...register('amount', { 
                    required: 'المبلغ مطلوب',
                    valueAsNumber: true,
                    min: { value: 0.01, message: 'المبلغ يجب أن يكون أكبر من 0' }
                  })}
                  className="input"
                  placeholder="0.00"
                />
                {errors.amount && (
                  <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                )}
              </div>
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">معلومات إضافية</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الوصف التفصيلي
                </label>
                <textarea
                  {...register('description')}
                  rows={3}
                  className="input resize-none"
                  placeholder="وصف تفصيلي للمصروف (اختياري)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اسم السائق
                </label>
                <input
                  type="text"
                  {...register('driver_name')}
                  className="input"
                  placeholder="اسم السائق (اختياري)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  إرفاق مستند أو صورة
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    {...register('attachment_url')}
                    className="input flex-1"
                    placeholder="رابط المرفق أو مسار الملف (اختياري)"
                  />
                  <button
                    type="button"
                    className="btn-outline flex items-center gap-2"
                    onClick={() => {
                      // TODO: Implement file upload functionality
                      alert('سيتم إضافة وظيفة رفع الملفات قريباً')
                    }}
                  >
                    <Upload className="h-4 w-4" />
                    رفع
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ملاحظات إضافية
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="input resize-none"
              placeholder="ملاحظات إضافية (اختياري)"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline"
              disabled={isSubmitting}
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحفظ...' : expense ? 'تحديث المصروف' : 'إضافة المصروف'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ExpenseForm
