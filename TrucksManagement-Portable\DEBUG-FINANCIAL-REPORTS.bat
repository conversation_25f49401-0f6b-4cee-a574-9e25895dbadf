@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🔍 تشخيص مشكلة التقارير المالية
echo ========================================
echo.
echo 🚨 المشكلة المبلغ عنها:
echo    ├── الرحلات المضافة لا تظهر في إجمالي الإيرادات
echo    ├── المصروفات المضافة لا تظهر في إجمالي المصروفات
echo    └── الصفحة الرئيسية تظهر 0 ريال في جميع الإحصائيات
echo.
echo 🔧 الإصلاحات المطبقة:
echo    ├── ✅ إضافة تحديث عناصر الصفحة الرئيسية
echo    ├── ✅ إضافة استدعاء updateHomeFinancialReports
echo    ├── ✅ إصلاح دوال حساب الإيرادات والمصروفات الشهرية
echo    ├── ✅ إضافة تسجيل مفصل للتشخيص
echo    ├── ✅ ربط التحديث بجميع عمليات الإضافة والتعديل
echo    └── ✅ إصلاح استدعاء التحديث عند التنقل للصفحة الرئيسية
echo.
echo 🌐 تشغيل التطبيق للتشخيص...
start "" "resources\app\dist\renderer\trucks-app.html"
echo.
echo ✅ تم تشغيل التطبيق!
echo.
echo 📋 خطة التشخيص:
echo.
echo 1️⃣  افتح وحدة تحكم المطور (F12)
echo    ├── اضغط F12 في المتصفح
echo    ├── انتقل إلى تبويب Console
echo    └── راقب الرسائل أثناء الاختبار
echo.
echo 2️⃣  اختبار إضافة رحلة جديدة:
echo    ├── انتقل إلى صفحة الرحلات
echo    ├── اضغط "تسجيل رحلة جديدة"
echo    ├── املأ البيانات واحفظ
echo    ├── راقب رسائل وحدة التحكم:
echo    │   ├── "🚚 معالجة رحلة: [التاريخ] [المبلغ]"
echo    │   ├── "📊 النتائج المحسوبة: {...}"
echo    │   └── "🔄 تحديث التقارير المالية..."
echo    └── تحقق من تحديث الصفحة الرئيسية
echo.
echo 3️⃣  اختبار إضافة مصروف جديد:
echo    ├── انتقل إلى صفحة المصروفات
echo    ├── اضغط "إضافة مصروف جديد"
echo    ├── املأ البيانات واحفظ
echo    ├── راقب رسائل وحدة التحكم:
echo    │   ├── "💰 معالجة مصروف: [التاريخ] [المبلغ]"
echo    │   ├── "📊 النتائج المحسوبة: {...}"
echo    │   └── "🔄 تحديث التقارير المالية..."
echo    └── تحقق من تحديث الصفحة الرئيسية
echo.
echo 4️⃣  فحص الصفحة الرئيسية:
echo    ├── انتقل إلى الصفحة الرئيسية
echo    ├── تحقق من الإحصائيات:
echo    │   ├── إجمالي الإيرادات
echo    │   ├── إجمالي المصروفات
echo    │   └── صافي الربح
echo    └── إذا كانت لا تزال 0، اضغط F5 لإعادة التحميل
echo.
echo 🔍 الأسباب المحتملة للمشكلة:
echo.
echo 💾 مشكلة في البيانات:
echo    ├── البيانات لا تحفظ بشكل صحيح
echo    ├── التواريخ بصيغة خاطئة
echo    └── حقل total غير محسوب
echo.
echo 🔄 مشكلة في التحديث:
echo    ├── دالة updateFinancialReports لا تستدعى
echo    ├── setTimeout يمنع التحديث الفوري
echo    └── DOM لا يتحدث بشكل صحيح
echo.
echo 📅 مشكلة في التواريخ:
echo    ├── التواريخ بصيغة مختلفة عن المتوقع
echo    ├── مقارنة التواريخ لا تعمل
echo    └── الشهر الحالي لا يتطابق
echo.
echo 🎯 الحلول المقترحة:
echo.
echo إذا رأيت في وحدة التحكم:
echo.
echo ✅ "🚚 معالجة رحلة" و "💰 معالجة مصروف":
echo    └── البيانات تتم معالجتها بشكل صحيح
echo.
echo ❌ لا توجد رسائل معالجة:
echo    ├── المشكلة في استدعاء updateFinancialReports
echo    ├── تحقق من أن الدالة تستدعى بعد الحفظ
echo    └── قد تحتاج إعادة تحميل الصفحة
echo.
echo 📊 "النتائج المحسوبة" تظهر 0:
echo    ├── مشكلة في مقارنة التواريخ
echo    ├── تحقق من صيغة التاريخ في البيانات
echo    └── قد تحتاج تعديل منطق المقارنة
echo.
echo 🔧 إصلاحات إضافية مقترحة:
echo.
echo 1️⃣  إضافة تحديث فوري:
echo    └── استدعاء updateFinancialReports مباشرة بدون setTimeout
echo.
echo 2️⃣  تحسين مقارنة التواريخ:
echo    └── استخدام Date objects بدلاً من string comparison
echo.
echo 3️⃣  إضافة تحديث شامل:
echo    └── إعادة حساب جميع الإحصائيات عند تحميل الصفحة
echo.
echo 4️⃣  تحسين حفظ البيانات:
echo    └── التأكد من حفظ جميع الحقول المطلوبة
echo.
echo ========================================
echo    📞 تقرير النتائج
echo ========================================
echo.
echo بعد الاختبار، يرجى الإبلاغ عن:
echo.
echo 1️⃣  ما تراه في وحدة تحكم المطور
echo 2️⃣  هل تتحدث الإحصائيات بعد إضافة البيانات؟
echo 3️⃣  ما هي قيم "النتائج المحسوبة" في وحدة التحكم؟
echo 4️⃣  هل تظهر رسائل "معالجة رحلة" و "معالجة مصروف"؟
echo.
echo 🎯 بناءً على النتائج، سأطبق الإصلاح المناسب!
echo.
pause
